'use client'
import { cn } from '@/utilities/cn'
import { cva } from 'class-variance-authority'
import { OmitUndefined } from 'class-variance-authority/types'
import { X as Close } from 'lucide-react'
import Image from 'next/image'
import React from 'react'
import { ToastContentProps } from 'react-toastify'

import successIcon from '@/assets/icons/success-notice.svg'

// Variant configuration for the toast
export const toastVariants = cva(
  'group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-4 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-top-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-top-full bg-white rounded-[8px] pr-8',
  {
    variants: {
      variant: {
        default: 'border-l-[3px] border-l-primary text-foreground bg-primary-50',
        informative: 'border-l-[3px] border-l-blue-400',
        success: 'border-l-[3px] border-l-custom-success-600 bg-custom-success-50',
        warning: 'border-l-[3px] border-l-yellow-400 bg-custom-warning-100',
        error: 'border-l-[3px] border-l-red-400 bg-danger-100',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
)

export const toastVariantsWeb = cva(
  'group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-4 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-top-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-top-full bg-white rounded-[8px] pr-8',
  {
    variants: {
      variant: {
        default: 'whitespace-nowrap text-foreground bg-primary-50',
        informative: 'whitespace-nowrap bg-informative-100',
        success: 'whitespace-nowrap bg-custom-success-50',
        warning: 'whitespace-nowrap bg-custom-warning-100',
        error: 'whitespace-nowrap bg-danger-100',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
)

// Type definitions
export type VariantProps<Component extends (...args: any) => any> = Omit<
  OmitUndefined<Parameters<Component>[0]>,
  'class' | 'className'
>

interface ToastProps extends VariantProps<typeof toastVariants>, Partial<ToastContentProps> {
  title?: string
  description?: string
  className?: string
}

// Toast component for displaying notifications
export const Toast: React.FC<ToastProps> = ({
  variant,
  title,
  description,
  className,
  closeToast,
}) => {
  return (
    <div className={cn(toastVariants({ variant }), className, 'relative shadow-md')}>
      <div className="space-y-1">
        {title && <ToastTitle variant={variant} title={title}></ToastTitle>}
        {description && <ToastDescription description={description}></ToastDescription>}
      </div>
      <span
        className="absolute right-4 top-4 cursor-pointer text-subdued transition-colors hover:text-black"
        onClick={closeToast}
      >
        <Close className="text-current"></Close>
      </span>
    </div>
  )
}

export const ToastWeb: React.FC<ToastProps> = ({
  variant,
  title,
  description,
  className,
  closeToast,
}) => {
  return (
    <div
      className={`relative flex items-center gap-2 space-y-1 shadow-md ${cn(toastVariantsWeb({ variant }), className)}`}
    >
      {variant === 'success' && <Image src={successIcon} alt="success" height={32} width={32} />}

      <ToastTitle className="!m-0" title={title} variant={variant} />

      {description && <ToastDescription description={description} />}

      <span
        className="!m-0 cursor-pointer text-subdued transition-colors hover:text-black"
        onClick={closeToast}
      >
        <Close className="text-current"></Close>
      </span>
    </div>
  )
}

export const ToastTitle = ({
  className,
  variant,
  title,
}: {
  title?: string
  description?: string
  className?: string
  variant?: VariantProps<typeof toastVariants>['variant']
}) => {
  // Define styles based on the variant
  const variantStyles = {
    default: 'typo-body-3 text-primary',
    informative: 'typo-body-3 text-blue-600',
    success: 'typo-body-3 text-custom-success-600',
    warning: 'typo-body-3 text-custom-warning-600',
    error: 'typo-body-3 text-danger-700',
  }

  // Fallback to "default" if the variant is null or undefined
  const variantClass = variant ? variantStyles[variant] : variantStyles.default

  return <div className={`${variantClass} ${className}`}>{title}</div>
}

export const ToastDescription = ({ description }: { description?: string }) => {
  return <div className={'typo-body-7'}>{description}</div>
}
