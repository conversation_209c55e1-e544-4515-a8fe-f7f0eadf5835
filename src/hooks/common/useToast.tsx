import { Toast, toastVariants, toastVariantsWeb, ToastWeb } from '@/components/ui/Toast/Toast'
import { cn } from '@/utilities/cn'
import { VariantProps } from 'class-variance-authority'
import { toast as toastifyAPI, ToastOptions } from 'react-toastify'

export const useToast = () => {
  const toast = ({
    variant = 'default',
    title,
    description,
    isMobileToast = true,
    options,
    toastClassname,
  }: {
    variant?: VariantProps<typeof toastVariants>['variant']
    title?: string
    description?: string
    isMobileToast?: boolean
    options?: Omit<ToastOptions, ''>
    toastClassname?: string
  }) => {
    const { className, ...rest } = options || {}
    toastifyAPI(
      <Toast
        variant={variant}
        title={title}
        description={description}
        className={toastClassname}
      />,
      {
        autoClose: 2000,
        className: cn(
          '!p-0 bg-transparent border-none mb-3 min-h-fit shadow-none rounded-none h-fit mobile-wrapper w-screen ',
          isMobileToast && '!w-[368px]',
          className,
        ),
        closeButton: false,
        hideProgressBar: true,
        pauseOnFocusLoss: false,
        draggable: false,
        pauseOnHover: false,
        delay: 0,
        ...rest,
      },
    )
  }

  const toastWeb = ({
    variant = 'default',
    title,
    description,
    options,
    toastClassname,
  }: {
    variant?: VariantProps<typeof toastVariantsWeb>['variant']
    title?: string
    description?: string
    options?: Omit<ToastOptions, ''>
    toastClassname?: string
  }) => {
    const { className, ...rest } = options || {}

    toastifyAPI(
      <ToastWeb
        variant={variant}
        title={title}
        description={description}
        className={toastClassname}
      />,
      {
        autoClose: 2000,
        className: cn(
          '!p-0 bg-transparent border-none min-h-fit shadow-none rounded-none h-fit',
          '!top-3 !right-3 !left-auto !bottom-auto !w-fit',
          className,
        ),
        closeButton: false,
        hideProgressBar: true,
        pauseOnFocusLoss: false,
        draggable: false,
        pauseOnHover: false,
        delay: 0,
        ...rest,
      },
    )
  }

  return { toast, toastWeb }
}
