import { favoriteKeywordService } from '@/services/favorite-keyword.service'
import { MutationOptions, useMutation } from '@tanstack/react-query'
import { useRef } from 'react'
import { favoriteKeywordMutationKeys } from './queryKeys'

type UpdateFavoriteKeywordVariables = { id: string; type: 'add' | 'delete' }

export const useUpdateFavoriteKeyword = ({
  options,
}: {
  options?: Omit<MutationOptions | undefined, 'mutationFn' | 'mutationKey'>
} = {}) => {
  // Define an AbortController to cancel previous requests
  const abortControllerRef = useRef<AbortController | null>(null)

  const {
    isError: isUpdateFavoriteKeywordError,
    isPending: isUpdateFavoriteKeywordPending,
    mutate: updateFavoriteKeywordMutation,
    mutateAsync: updateFavoriteKeywordMutationAsync,
    ...rest
  } = useMutation({
    mutationKey: favoriteKeywordMutationKeys['update-favorite-keyword'].base(),
    mutationFn: async ({ id, type }: UpdateFavoriteKeywordVariables) => {
      // Abort any ongoing request before initiating a new one
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }

      // Create a new AbortController for the new request
      const abortController = new AbortController()
      abortControllerRef.current = abortController

      // Pass the signal from AbortController to the service method
      if (type === 'add') {
        return favoriteKeywordService.addFavoriteKeywords(id, {
          signal: abortControllerRef.current.signal,
        })
      } else if (type === 'delete') {
        return favoriteKeywordService.deleteFavoriteKeywords(id, {
          signal: abortControllerRef.current.signal,
        })
      }

      return Promise.resolve()
    },
    ...options,
  })

  return {
    isUpdateFavoriteKeywordError,
    isUpdateFavoriteKeywordPending,
    updateFavoriteKeywordMutation,
    updateFavoriteKeywordMutationAsync,
    ...rest,
  }
}
