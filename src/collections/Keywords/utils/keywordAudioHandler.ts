import { SUPPORTED_TEXT_TO_SPEECH_VOICE } from '@/constants/locale.constant'
import { LocaleEnum } from '@/enums/locale.enum'
import { TextToSpeechClient } from '@google-cloud/text-to-speech'
import { APIError, PayloadRequest, File as PFile } from 'payload'

interface GenerateKeywordAudioOptions {
  keywordId: string
  language: string
  req: PayloadRequest
}

export const generateKeywordAudio = async ({
  keywordId,
  language,
  req,
}: GenerateKeywordAudioOptions) => {
  const voiceConfig = SUPPORTED_TEXT_TO_SPEECH_VOICE[language]

  if (!voiceConfig) {
    throw new APIError('No Supported Language', 400)
  }

  const keyword = await req.payload.findByID({
    collection: 'keywords',
    id: keywordId,
    locale: language as LocaleEnum,
  })

  if (!keyword) {
    throw new APIError('Keyword not found', 404)
  }

  const { audio } = keyword

  // Check existing audio for the current language
  let existingAudio: any = null
  if (audio && Array.isArray(audio)) {
    existingAudio = audio.find((item) => item.language === language)
  }

  if (existingAudio && existingAudio.audio && existingAudio.audio.url) {
    return { url: existingAudio.audio.url }
  }

  // Use the keyword name as the text for speech conversion
  const text = keyword.name

  if (!text) {
    throw new APIError('Keyword has no name in the requested language', 400)
  }

  // Construct the request
  const request = {
    input: { text: text },
    voice: voiceConfig,
    // select the type of audio encoding
    audioConfig: { audioEncoding: 'MP3' as any },
  }

  const client = new TextToSpeechClient()

  // Performs the text-to-speech request
  const [response] = await client.synthesizeSpeech(request)

  const fileBuffer = Buffer.from(response.audioContent as string, 'base64')
  const size = fileBuffer.byteLength
  const mimetype = 'audio/mp3'
  const name = `keyword-audio-${keywordId}-${language}-${Date.now()}`

  const audioFile = {
    data: fileBuffer,
    mimetype,
    name,
    size,
  } as PFile

  const media = await req.payload.create({
    collection: 'media',
    data: {
      filename: name,
      filesize: size,
      mimeType: mimetype,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      alt: '',
    },
    file: audioFile,
  })

  // Find and replace new audio to Keyword
  let newAudio: any = []

  if (keyword.audio) {
    newAudio = keyword.audio
  }

  const audioToUpdate = newAudio.find((item: any) => item.language === language)

  if (audioToUpdate) {
    audioToUpdate.audio = media.id
  } else {
    newAudio.push({
      language,
      audio: media.id,
    })
  }

  await req.payload.update({
    collection: 'keywords',
    id: keywordId,
    data: {
      audio: newAudio,
    },
  })

  console.log(`Audio content written to file: ${media.id}`)

  return { url: media.url }
}
