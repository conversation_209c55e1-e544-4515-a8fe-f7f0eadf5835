import { PayloadRequest } from 'payload'

import { LocaleEnum } from '@/enums/locale.enum'
import { withErrorHandling } from '@/utils/errorHandlers'

export const getKeywordDetailsImpl = async (req: PayloadRequest) => {
  const user = req?.user
  const queries = req.query
  const { id } = req.routeParams || {}
  const { locale, where, convertDescriptionHTML, ...other } = queries

  if (!id) {
    return Response.json({ error: 'Keyword ID is required' }, { status: 400 })
  }

  const data = await req.payload.findByID({
    collection: 'keywords',
    id: id as string,
    locale: (locale as LocaleEnum) || LocaleEnum.VI,

    ...other,
    req,
    context: {
      convertDescriptionHTML: true,
    },
  })

  if (!data) {
    return Response.json({ error: 'Keyword not found' }, { status: 404 })
  }

  if (user) {
    const currentFavorite = await req.payload.find({
      collection: 'favorite-keywords',
      where: {
        and: [
          {
            'keyword.id': { equals: data.id },
            'user.id': { equals: user?.id },
          },
        ],
      },
      pagination: false,
      select: { keyword: true },
      depth: 0,
    })

    // Add isFavorite flag to the main keyword only
    const updatedData = {
      ...data,
      isFavorite: currentFavorite.docs.length > 0,
    }

    return Response.json(updatedData || null)
  }

  return Response.json(data || null)
}

export const getKeywordDetailsHandler = withErrorHandling(
  getKeywordDetailsImpl,
  'An unexpected error occurred while fetching keyword details',
)
