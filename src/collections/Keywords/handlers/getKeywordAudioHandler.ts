import { LocaleEnum } from '@/enums/locale.enum'
import { PayloadRequest } from 'payload'
import { generateKeywordAudio } from '../utils/keywordAudioHandler'

export const getKeywordAudioHandler = async (req: PayloadRequest) => {
  if (!req || !req.json) {
    return Response.json(
      {
        error: 'Request are missing.',
      },
      { status: 400 },
    )
  }
  const { audioLanguage } = req.query || {}
  const { keywordId } = req.routeParams || {}

  const language = (audioLanguage as string) || LocaleEnum.VI

  if (!keywordId) {
    return Response.json({ error: 'No keywordId provided' }, { status: 400 })
  }

  const result = await generateKeywordAudio({
    keywordId: keywordId as string,
    language,
    req,
  })

  return Response.json(result, { status: 200 })
}
