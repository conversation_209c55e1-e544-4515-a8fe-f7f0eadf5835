import { DIFY_DATASETS } from '@/features/dify/constants/dify.constant'
import {
  syncDifyDocument,
  type DifyDocumentSyncConfig,
} from '@/features/dify/utils/difyDocumentSync'
import {
  buildMedicinesMetadata,
  buildProductsMetadata,
} from '@/features/dify/utils/difyMetadataBuilder'
import { Media, Product } from '@/payload-types'
import { mediaService } from '@/services/media.service'
import { lexicalToPlainText } from '@/utilities/lexicalToPlainText'
import type { CollectionAfterChangeHook } from 'payload'

export const syncDifyDocumentAfterChangeProduct: CollectionAfterChangeHook<Product> = async ({
  doc,
  req,
}) => {
  if (doc.unverified) {
    return doc
  }
  // Prevent infinite loop from hook-triggered updates
  if (req.context?.fromAfterChangeHook) {
    return doc
  }

  // Skip if document is invalid
  if (!doc || typeof doc !== 'object') {
    return doc
  }

  // Get media if hero image exists
  let media: Media | null = null
  if (doc.heroImage) {
    try {
      media = await mediaService.getMedia({
        id: doc.heroImage as string,
      })
    } catch (error) {
      console.error('Error getting media for product:', error)
    }
  }

  // Prepare document content

  const mergedContraindicationsText =
    '###Contraindications: ' + lexicalToPlainText(doc.contraindications || null) + '\n\n'
  const mergedDosageText = '###Dosage: ' + lexicalToPlainText(doc.dosage || null) + '\n\n'
  const mergedDosageFormText =
    '###DosageForm: ' + lexicalToPlainText(doc.dosageForm || null) + '\n\n'
  const mergedIngredientText =
    '###Ingredient: ' + lexicalToPlainText(doc.ingredient || null) + '\n\n'
  const mergedSpecificationText =
    '###Specification: ' + lexicalToPlainText(doc.specification || null) + '\n\n'
  const mergedUsesText = '###Uses: ' + lexicalToPlainText(doc.uses || null) + '\n\n'

  // Merge each titled section together using " || " as a separator
  const contentText = [
    mergedContraindicationsText,
    mergedDosageText,
    mergedDosageFormText,
    mergedIngredientText,
    mergedSpecificationText,
    mergedUsesText,
  ].join(' || ')

  const mergedText = `Title: ${doc.title || ''} === Description: ${doc.description || lexicalToPlainText(doc.uses || null) || ''} === Details: ${contentText}`

  // Set mergedText on document
  doc.mergedText = mergedText

  const documentPayload = {
    name: doc.title,
    text: mergedText,
  }

  // Build metadata list dynamically from API
  let metadataPayload: DifyDocumentSyncConfig['metadataPayload'] = undefined

  try {
    const metadataList = await buildProductsMetadata({
      datasetId: DIFY_DATASETS.PRODUCTS,
      docSlug: doc.slug as string,
      imageUrl: media?.thumbnailURL || media?.url || undefined,
      description: doc.description || lexicalToPlainText(doc.uses || null) || undefined,
    })

    metadataPayload = {
      operation_data: [
        {
          document_id: '',
          metadata_list: metadataList,
        },
      ],
    }
  } catch (error) {
    console.warn(
      'Failed to build metadata for product document, proceeding without metadata:',
      error,
    )
    // metadataPayload remains undefined, sync will proceed without metadata
  }

  const datasetId = DIFY_DATASETS.PRODUCTS

  const syncConfig: DifyDocumentSyncConfig = {
    configKey: 'PRODUCTS',
    datasetId,
    collection: 'products',
    difyDocumentIdField: 'difyDocumentId',
    documentPayload,
    metadataPayload,
  }

  try {
    await syncDifyDocument({
      config: syncConfig,
      currentDifyDocumentId: doc.difyDocumentId || null,
      req,
      doc,
      mergedText,
      locale: req.locale,
    })
  } catch (error) {
    console.error('Error syncing Dify document:', error)
  }

  return doc
}
