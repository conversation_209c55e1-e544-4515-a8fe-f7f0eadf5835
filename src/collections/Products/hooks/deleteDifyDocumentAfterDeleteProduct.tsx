import { handleDifyDocumentDeletion } from '@/features/dify/utils/difyDocumentSync'
import { Product } from '@/payload-types'
import type { CollectionAfterDeleteHook } from 'payload'

export const deleteDifyDocumentAfterDeleteProduct: CollectionAfterDeleteHook<Product> = async ({
  doc,
}) => {
  try {
    // Handle deletion of Dify documents when medicine is deleted
    await handleDifyDocumentDeletion({ doc, collection: 'products' })
  } catch (error) {
    console.error('Error deleting Dify document for product:', error)
    // Continue execution even if Dify deletion fails
  }

  return doc
}
