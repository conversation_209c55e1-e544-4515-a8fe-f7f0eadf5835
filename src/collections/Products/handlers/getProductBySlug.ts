import { PayloadRequest, Where } from 'payload'

export const getProductBySlugHandler = async (req: PayloadRequest) => {
  // Validate the incoming request object
  if (!req?.json || !req?.routeParams) {
    return Response.json({ error: 'Request or route parameters are missing.' }, { status: 400 })
  }
  const queries = req.query
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { where, select, populate, ...otherQueries } = queries
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { or, and, ...rest } = (where as Where) || {}

  const { slug } = req.routeParams
  const user = req?.user as any

  // Fetch medicine data
  const data = await req.payload.find({
    collection: 'products',
    draft: false,
    limit: 1,
    depth: 5,
    pagination: false,
    where: { slug: { equals: slug } },

    ...otherQueries,
  })

  const product = data?.docs?.[0] || null
  if (!product) {
    return Response.json(null)
  }
  // const availableIn = medicine?.availableIn
  const currentFavorites = await req.payload.find({
    collection: 'favorite-products',
    where: {
      and: [
        {
          'product.id': { equals: product?.id },
          'user.id': { equals: user?.id },
        },
      ],
    },
    pagination: false,
    select: { product: true, user: true },
    depth: 2,
    limit: 1,
  })

  const isFavorite = currentFavorites?.docs?.length > 0

  product['isFavorite'] = isFavorite
  return Response.json(data?.docs?.[0] || null)
}
