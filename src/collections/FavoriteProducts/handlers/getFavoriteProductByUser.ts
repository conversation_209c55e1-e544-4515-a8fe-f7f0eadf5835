import { authenticatedWithValidSession } from '@/access/authenticatedWithValidSession'
import { LocaleEnum } from '@/enums/locale.enum'
import { ProductCategory } from '@/payload-types'
import { withErrorHandling } from '@/utils/errorHandlers'
import { APIError, PayloadRequest, Where } from 'payload'

const getFavoriteProductByUserHandler = async (req: PayloadRequest) => {
  const isValid = await authenticatedWithValidSession({ req })

  if (!isValid) {
    throw new APIError('MES-448', 401)
  }

  const user = req.user!
  const { limit, page, locale, where, categories, ...otherQueries } = req.query

  // Normalize categories param into array
  const listCategories = Array.isArray(categories) ? categories : categories ? [categories] : []

  // Fetch lft/rgt for provided categories
  const categoriesData: ProductCategory[] =
    listCategories.length > 0
      ? await Promise.allSettled(
          listCategories.map((category) =>
            req.payload.findByID({
              collection: 'product-categories',
              id: category,
              select: { lft: true, rgt: true },
            }),
          ),
        ).then((results) =>
          results
            .filter(
              (result): result is PromiseFulfilledResult<ProductCategory> =>
                result.status === 'fulfilled',
            )
            .map((result) => result.value),
        )
      : []

  // Collect all descendant category IDs in parallel
  const descendantResults =
    categoriesData.length > 0
      ? await Promise.allSettled(
          categoriesData.map((category) =>
            req.payload.find({
              collection: 'product-categories',
              where: {
                and: [
                  { lft: { greater_than_equal: category.lft } },
                  { rgt: { less_than_equal: category.rgt } },
                ],
              },
              select: { id: true, lft: true, rgt: true },
              pagination: false,
            }),
          ),
        )
      : []

  let categoryIDs: string[] = []
  for (const result of descendantResults) {
    if (result.status === 'fulfilled') {
      categoryIDs.push(...result.value.docs.map((c: ProductCategory) => c.id as string))
    }
  }

  // Remove duplicates
  categoryIDs = [...new Set(categoryIDs)]

  // Handle where clause similar to getFilteredProducts pattern
  const { or, and, ...rest } = (where as Where) || {}
  const dynamicWhere: Where = {
    ...rest,
    ...(or && { or }),
    ...(and && { and }),
  }

  // Build final where clause with user filtering and categories
  const finalWhere: Where = {
    and: [
      { user: { equals: user.id } },
      ...(categoryIDs.length > 0 ? [{ 'product.categories': { in: categoryIDs } }] : []),
      dynamicWhere,
    ],
  }

  const data = await req.payload.find({
    collection: 'favorite-products',
    where: finalWhere,
    locale: locale as LocaleEnum,
    depth: 5,
    select: { product: true },
    sort: '-createdAt',
    limit: limit as number | undefined,
    page: (page as number) ?? 1,
    ...otherQueries,
  })

  const formattedData = {
    ...data,
    docs: data.docs.map((doc) => ({
      ...doc,
      product: Object.assign({}, doc.product || {}, { isFavorite: true }),
    })),
  }

  return Response.json(formattedData, { status: 200 })
}

// Wrap the handler with standardized error handling
export const getFavoriteProductByUser = withErrorHandling(
  getFavoriteProductByUserHandler,
  'An unexpected error occurred while fetching favorite products',
)
