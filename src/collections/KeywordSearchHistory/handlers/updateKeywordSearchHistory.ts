import { authenticatedWithValidSession } from '@/access/authenticatedWithValidSession'
import { EXCEPTION_STATUS } from '@/constants/exceptionStatus.constant'
import { Keyword } from '@/payload-types'
import { withErrorHandling } from '@/utils/errorHandlers'
import { APIError, PayloadRequest } from 'payload'

const updateKeywordSearchHistoryHandler = async (req: PayloadRequest) => {
  const isValid = await authenticatedWithValidSession({ req })

  if (!isValid) {
    throw new APIError('MES-448', EXCEPTION_STATUS.SESSION_INVALID.code)
  }

  const user = req.user!

  // Validate the incoming request object
  if (!req || !req.routeParams) {
    throw new APIError('Request or route parameters are missing.', 400)
  }

  const { keywordId: id } = req.routeParams

  if (!id) {
    throw new APIError('Missing keyword ID', 400)
  }
  let keyword: Keyword | null = null
  try {
    keyword = await req.payload.findByID({
      collection: 'keywords',
      id: id as string,
    })
  } catch (error) {
    console.log(error)
  }

  if (!keyword) {
    throw new APIError('Keyword not found', 404)
  }

  // Check if keyword search history already exists for this user
  const existingHistory = await req.payload.find({
    collection: 'keyword-search-history',
    where: {
      'keyword.id': { equals: id },
      'user.id': {
        equals: user?.id,
      },
    },
    limit: 1,
    pagination: false,
  })

  if (existingHistory?.docs.length) {
    // Update existing entry's updatedAt field
    await req.payload.update({
      collection: 'keyword-search-history',
      id: existingHistory.docs[0].id,
      data: {
        updatedAt: new Date().toISOString(),
      },
    })

    return Response.json(
      { message: 'Keyword search history updated successfully' },
      { status: 200 },
    )
  } else {
    // Create new entry
    await req.payload.create({
      collection: 'keyword-search-history',
      data: {
        user: user.id,
        keyword: keyword.id,
      },
    })

    return Response.json({ message: 'Keyword search history added successfully' }, { status: 201 })
  }
}

// Wrap the handler with standardized error handling
export const updateKeywordSearchHistory = withErrorHandling(
  updateKeywordSearchHistoryHandler,
  'An unexpected error occurred while updating keyword search history',
)
