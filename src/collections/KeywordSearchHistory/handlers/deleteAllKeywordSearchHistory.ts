import { authenticatedWithValidSession } from '@/access/authenticatedWithValidSession'
import { EXCEPTION_STATUS } from '@/constants/exceptionStatus.constant'
import { withErrorHandling } from '@/utils/errorHandlers'
import { APIError, PayloadRequest } from 'payload'

const deleteAllKeywordSearchHistoryHandler = async (req: PayloadRequest) => {
  const isValid = await authenticatedWithValidSession({ req })

  if (!isValid) {
    throw new APIError('MES-448', EXCEPTION_STATUS.SESSION_INVALID.code)
  }

  const user = req.user!

  // Delete all keyword search history entries for this user
  const result = await req.payload.delete({
    collection: 'keyword-search-history',
    where: {
      'user.id': {
        equals: user.id,
      },
    },
  })

  return Response.json(
    {
      message: 'All keyword search history deleted successfully',
      deletedCount: result.docs.length,
    },
    { status: 200 },
  )
}

// Wrap the handler with standardized error handling
export const deleteAllKeywordSearchHistory = withErrorHandling(
  deleteAllKeywordSearchHistoryHandler,
  'An unexpected error occurred while deleting all keyword search history',
)
