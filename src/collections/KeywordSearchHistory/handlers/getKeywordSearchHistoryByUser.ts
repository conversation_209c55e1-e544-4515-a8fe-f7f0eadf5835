import { authenticatedWithValidSession } from '@/access/authenticatedWithValidSession'
import { LocaleEnum } from '@/enums/locale.enum'
import { cleanAndConvertSelectObject, SelectObject } from '@/utilities/cleanAndConvertSelectObject'
import { withErrorHandling } from '@/utils/errorHandlers'
import { APIError, PayloadRequest, Where } from 'payload'

const getKeywordSearchHistoryByUserHandler = async (req: PayloadRequest) => {
  const isValid = await authenticatedWithValidSession({ req })

  if (!isValid) {
    throw new APIError('MES-448', 401)
  }

  const user = req.user!
  const { limit, page, locale, where, select, populate, ...otherQueries } = req.query

  const { or, and, ...rest } = (where as Where) || {}
  const dynamicWhere: Where = {
    ...rest,
    ...(or && { or }),
    ...(and && { and }),
  }

  // Build final where clause with user filtering
  const finalWhere: Where = {
    and: [{ user: { equals: user.id } }, dynamicWhere],
  }
  const formatPopulate = populate
    ? cleanAndConvertSelectObject(populate as SelectObject)
    : {
        keywords: {
          name: true,
          hiragana: true,
        },
      }

  const data = await req.payload.find({
    collection: 'keyword-search-history',
    where: finalWhere,
    locale: locale as LocaleEnum,
    depth: 5,
    select: { keyword: true },
    sort: '-updatedAt',
    limit: limit as number | undefined,
    page: (page as number) ?? 1,
    populate: formatPopulate,
    ...otherQueries,
  })

  return Response.json(data, { status: 200 })
}

// Wrap the handler with standardized error handling
export const getKeywordSearchHistoryByUser = withErrorHandling(
  getKeywordSearchHistoryByUserHandler,
  'An unexpected error occurred while fetching keyword search history',
)
