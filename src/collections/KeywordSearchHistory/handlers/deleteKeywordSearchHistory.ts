import { authenticatedWithValidSession } from '@/access/authenticatedWithValidSession'
import { EXCEPTION_STATUS } from '@/constants/exceptionStatus.constant'
import { withErrorHandling } from '@/utils/errorHandlers'
import { APIError, PayloadRequest } from 'payload'

const deleteKeywordSearchHistoryHandler = async (req: PayloadRequest) => {
  const isValid = await authenticatedWithValidSession({ req })

  if (!isValid) {
    throw new APIError('MES-448', EXCEPTION_STATUS.SESSION_INVALID.code)
  }

  const user = req.user

  // Validate the incoming request object
  if (!req || !req.routeParams) {
    throw new APIError('Request or route parameters are missing.', 400)
  }

  const { id } = req.routeParams

  if (!id) {
    throw new APIError('Missing history ID', 400)
  }

  // Delete keyword search history entry for this user and specific keyword
  const result = await req.payload.delete({
    collection: 'keyword-search-history',
    where: {
      'user.id': {
        equals: user?.id,
      },
      id: {
        equals: id,
      },
    },
  })

  return Response.json(
    {
      message: 'Keyword search history deleted successfully',
    },
    { status: 200 },
  )
}

// Wrap the handler with standardized error handling
export const deleteKeywordSearchHistory = withErrorHandling(
  deleteKeywordSearchHistoryHandler,
  'An unexpected error occurred while deleting keyword search history',
)
