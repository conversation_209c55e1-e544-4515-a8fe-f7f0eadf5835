import { admins } from '@/access/admins'
import { CollectionConfig } from 'payload'
import { deleteAllKeywordSearchHistory } from './handlers/deleteAllKeywordSearchHistory'
import { deleteKeywordSearchHistory } from './handlers/deleteKeywordSearchHistory'
import { getKeywordSearchHistoryByUser } from './handlers/getKeywordSearchHistoryByUser'
import { updateKeywordSearchHistory } from './handlers/updateKeywordSearchHistory'

export const KeywordSearchHistory: CollectionConfig = {
  slug: 'keyword-search-history',
  access: {
    read: () => true,
    create: admins,
    update: admins,
    delete: admins,
  },
  admin: {
    group: 'Users',
  },
  fields: [
    {
      name: 'keyword',
      type: 'relationship',
      relationTo: 'keywords',
    },
    {
      name: 'user',
      type: 'relationship',
      relationTo: 'users',
    },
  ],
  endpoints: [
    {
      path: '/user',
      method: 'get',
      handler: getKeywordSearchHistoryByUser,
    },
    {
      path: '/update-history/:keywordId',
      method: 'post',
      handler: updateKeywordSearchHistory,
    },
    {
      path: '/delete/:id',
      method: 'delete',
      handler: deleteKeywordSearchHistory,
    },
    {
      path: '/delete-all',
      method: 'delete',
      handler: deleteAllKeywordSearchHistory,
    },
  ],
}
