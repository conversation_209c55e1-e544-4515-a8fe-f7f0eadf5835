import PostsDetailWrapper from '@/features/post/desktop/PostsDetail/PostsDetailLayout/PostDetailWrapper'
import { postService } from '@/services/post.service'
import { getLocale } from 'next-intl/server'
import { cache } from 'react'

type Args = {
  params: Promise<{
    slug?: string
    locale: string
  }>
}

export default async function NewsDetailPage({ params: paramsPromise }: Args) {
  const locale = await getLocale()
  const { slug = '' } = await paramsPromise

  const post = await queryPostBySlug({ slug, locale })

  return (
    <div className="h-full bg-custom-background-hover px-16 py-6">
      {post && <PostsDetailWrapper post={post} />}
    </div>
  )
}

const queryPostBySlug = cache(async ({ slug, locale }: { slug: string; locale: string }) => {
  const result = await postService.getPosts({
    params: {
      // draft: false,
      limit: 1,
      depth: 2,
      pagination: false,
      where: {
        slug: {
          equals: slug,
        },
        language: {
          equals: locale,
        },
      },
    },
    options: {
      cache: 'no-store',
      next: {},
    },
  })

  return result?.docs?.[0] || null
})
