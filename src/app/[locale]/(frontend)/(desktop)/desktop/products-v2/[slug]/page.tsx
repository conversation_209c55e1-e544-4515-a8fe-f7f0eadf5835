import ProductDetailWrapper from '@/features/product-v2/desktop/layout/ProductDetailWrapper'
type SearchParams = Promise<{ [key: string]: string | undefined }>
type Params = Promise<{ slug: string }>

export default async function ProductDetailPage({
  searchParams,
  params,
}: {
  searchParams: SearchParams
  params: Params
}) {
  const paramsValue = await searchParams
  const paramsProps = await params

  return <ProductDetailWrapper paramsValue={paramsValue} params={paramsProps} />
}
