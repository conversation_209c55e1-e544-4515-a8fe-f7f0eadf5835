import { useTranslations } from 'next-intl'

type FunctionalFoodRelatedType = {
  functionalFoodData: any[]
}

const FunctionalFoodRelated: React.FC<FunctionalFoodRelatedType> = ({ functionalFoodData }) => {
  const t = useTranslations()
  return (
    functionalFoodData &&
    functionalFoodData.length > 0 && (
      <div className="rounded-xl bg-white p-4">
        <div className="flex flex-col gap-2">
          <div className="space-y-3">
            <h4 className="typo-body-3">{t('MES-739')}</h4>
            <div className="space-y-3">{functionalFoodData?.map((_item: any) => <>data</>)}</div>
          </div>
        </div>
      </div>
    )
  )
}

export default FunctionalFoodRelated
