import { PostCardItem } from '@/features/post/components/PostCardItem/PostCardItem'
import { Post } from '@/payload-types'
import { useTranslations } from 'next-intl'

type PostRelatedType = {
  relatedPostsFilter: Post[]
}
const PostRelated: React.FC<PostRelatedType> = ({ relatedPostsFilter }) => {
  const t = useTranslations()
  return (
    relatedPostsFilter &&
    relatedPostsFilter.length > 0 && (
      <div className="rounded-xl bg-white p-4">
        <div className="flex flex-col gap-2">
          <div className="space-y-3">
            <h4 className="typo-body-3">{t('MES-78')}</h4>
            <div className="space-y-3">
              {relatedPostsFilter?.map((post: Post) => (
                <PostCardItem key={post.id} post={post} variant="row"></PostCardItem>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  )
}

export default PostRelated
