import { useTranslations } from 'next-intl'
import Image from 'next/image'

import arrowRightIcon from '@/assets/icons/arrow-right-gray.svg'

type MedicalDepartmentType = {
  medicalDepartment: any[]
}

const MedicalDepartment: React.FC<MedicalDepartmentType> = ({ medicalDepartment }) => {
  const t = useTranslations()
  return (
    medicalDepartment &&
    medicalDepartment.length > 0 && (
      <div className="rounded-xl bg-white p-4">
        <div className="flex flex-col gap-2">
          <div className="space-y-3">
            <h4 className="typo-body-3">{t('MES-740')}</h4>
            <div className="space-y-3">
              {medicalDepartment?.map((_item: any) => (
                <div
                  key={_item.id}
                  className="flex cursor-pointer items-center justify-between gap-2 rounded-lg bg-custom-background-hover px-4 py-2"
                >
                  <div className="flex items-center gap-3">
                    <Image src={_item.src} alt={_item.title} height={32} width={32} />
                    {_item.title}
                  </div>

                  <Image src={arrowRightIcon} alt={'arrow-right'} height={20} width={20} />
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  )
}

export default MedicalDepartment
