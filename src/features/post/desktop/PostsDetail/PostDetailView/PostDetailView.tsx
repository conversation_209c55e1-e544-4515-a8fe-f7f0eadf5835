import React from 'react'
import PostReading from './PostReading/PostReading'
import { Post } from '@/payload-types'
import PostRelated from './PostRelated/PostRelated'
import FunctionalFoodRelated from './FunctionalFoodRelated/FunctionalFoodRelated'
import MedicalDepartment from './MedicalDepartment/MedicalDepartment'

type PostDetailView = {
  post: Post
}
const PostDetailView: React.FC<PostDetailView> = ({ post }) => {
  const { relatedPosts } = post

  const relatedPostsFilter: Post[] = Array.isArray(relatedPosts)
    ? relatedPosts.filter((post): post is Post => typeof post === 'object')
    : []

  return (
    <div className="flex items-start gap-4">
      <div className="flex-[3] shrink-0 rounded-xl bg-white">
        <PostReading post={post} />
      </div>

      <div className="flex h-fit flex-[1] shrink-0 flex-col gap-3">
        <MedicalDepartment medicalDepartment={[]} />

        <PostRelated relatedPostsFilter={relatedPostsFilter} />

        <FunctionalFoodRelated functionalFoodData={[]} />
      </div>
    </div>
  )
}

export default PostDetailView
