'use client'

import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { LocaleEnum } from '@/enums/locale.enum'
import {
  FilterField,
  useGenerateSearchQueryFilter,
} from '@/hooks/common/useGenerateSearchQueryFilter'
import { useGetPosts } from '@/hooks/query/post/useGetPosts'
import { cn } from '@/utilities/cn'
import { useLocale, useTranslations } from 'next-intl'
import { parse } from 'qs-esm'

import { PaginationWithQuery } from '@/components/ui/Pagination/PaginationWithQuery'
import { Post } from '@/payload-types'
import { APP_ROUTES } from '@/routes'
import { useRouter } from 'next/navigation'
import { useCallback, useEffect, useRef, useState } from 'react'
import { PostCardItem } from '../PostCardItem/PostCardItem'
import PostsModalFilter from '../PostsModalFilter/PostsModalFilter'

interface PostsSearchProps {
  params?: {
    [key: string]: string | undefined
  }
}

const SEARCHABLE_FIELDS: FilterField[] = [
  { path: 'categories.title' },
  { path: 'keywords.keyword', type: 'like' },
  { path: 'title' },
]
export const PostsSearch: React.FC<PostsSearchProps> = ({ params: searchParams }) => {
  const t = useTranslations()
  const router = useRouter()

  const locale = useLocale()
  const searchKeywordsRef = useRef<string[]>([])

  const { page } = searchParams || {}
  const { q: query } = parse(searchParams as unknown as string)

  const [currentPage, setCurrentPage] = useState(() => page || 1)
  const [filterState, setFilterState] = useState<{
    selectedCategories: string[]
    selectedLanguage?: string
  }>({
    selectedCategories: [],
    selectedLanguage: undefined,
  })

  useEffect(() => {
    setCurrentPage(page || 1)
  }, [page])

  const { queryFilters, generateQueryString } = useGenerateSearchQueryFilter({
    query,
    searchAbleFields: SEARCHABLE_FIELDS,
  })

  const getKeywordsFromParams = useCallback(
    (params?: { [key: string]: string | undefined }): string[] => {
      const keywords: string[] = []
      if (params) {
        Object.keys(params).forEach((key) => {
          if (key.startsWith('q[') && key.endsWith(']')) {
            const index = parseInt(key.slice(2, -1))
            if (!isNaN(index) && params[key]) {
              keywords[index] = params[key] as string
            }
          }
        })
      }
      return keywords.filter(Boolean)
    },
    [],
  )

  useEffect(() => {
    const keywords = getKeywordsFromParams(searchParams)
    searchKeywordsRef.current = keywords
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchParams])

  // Create additional filter conditions based on state
  const createFilterConditions = () => {
    const conditions: any[] = []

    // Add category filter if categories are selected
    if (filterState.selectedCategories.length > 0) {
      conditions.push({
        'categories.title': {
          in: filterState.selectedCategories,
        },
      })
    }

    // Add language filter if specified (optional)
    if (filterState.selectedLanguage) {
      conditions.push({
        language: { equals: filterState.selectedLanguage },
      })
    }

    return conditions
  }

  const { posts, isGetPostsLoading } = useGetPosts({
    params: {
      depth: 1,
      limit: 16,
      locale: locale || LocaleEnum.VI,
      fallbackLocale: false,
      page: currentPage ? Number(currentPage) : 1,
      where: {
        and: [
          {
            // _status: { equals: 'published' },
            language: { equals: locale || 'vi' },
            or: queryFilters,
          },
          ...createFilterConditions(),
        ],
      },
      select: {
        title: true,
        heroImage: true,
        createdAt: true,
        id: true,
        slug: true,
        categories: true,
      },
    },
  })

  const handleFilter = ({ params, type }: { params: string[]; type: 'close' | 'filter' }) => {
    const queryString = generateQueryString(searchKeywordsRef?.current || [])
    const url = `${APP_ROUTES.POSTS.path}?page=1${queryString ? `&${queryString}` : ''}`
    if (type === 'filter') {
      setFilterState((prev) => ({
        ...prev,
        selectedCategories: params,
      }))
      setCurrentPage(1)

      router.replace(url)
    } else if (type === 'close') {
      clearFilters()
      setCurrentPage(1)
      router.replace(url)
    }
  }

  // Function to clear all filters
  const clearFilters = () => {
    setFilterState({
      selectedCategories: [],
      selectedLanguage: undefined,
    })
  }

  return isGetPostsLoading ? (
    <div className="mt-3 grid grid-cols-2 gap-3">
      {Array.from({ length: 8 }, (_, index) => (
        <div key={index} className={cn('relative flex gap-3')}>
          <Skeleton
            className={cn('relative h-[92px] w-[136px] overflow-hidden rounded-lg')}
          ></Skeleton>
          <div className="flex-1 space-y-2">
            <Skeleton className="h-5 w-8"></Skeleton>
            <Skeleton className="h-5 w-12"></Skeleton>
            <Skeleton className="h-8 w-full"></Skeleton>
          </div>
        </div>
      ))}
    </div>
  ) : (
    <>
      <div className="mt-3 flex items-center justify-between gap-2">
        <div className="typo-body-3">
          {t('MES-71')}: (<span>{posts?.totalDocs ?? 0}</span>)
        </div>
        <PostsModalFilter
          onFilter={handleFilter}
          selectedCategories={filterState.selectedCategories}
          selectedLanguage={filterState.selectedLanguage}
        />
      </div>

      <div className="mt-3 grid grid-cols-2 gap-3">
        {posts && posts?.docs?.length > 0 ? (
          posts.docs.map((post) => (
            <PostCardItem variant="row" post={post as Omit<Post, 'content'>} key={post.id} />
          ))
        ) : (
          <div className="typo-body-7 col-span-2 mt-3 h-[52px] w-full rounded-lg bg-danger-200 p-4 text-danger-600">
            {t('MES-72')}
          </div>
        )}
      </div>

      {/* Pagination */}
      {posts && posts?.docs?.length > 0 && posts?.totalPages >= 1 && (
        <div className="mt-4">
          <PaginationWithQuery
            totalPage={posts?.totalPages}
            initialPage={Number(page) || 1}
            queryUrl={{
              pathname: APP_ROUTES.POSTS.path,
            }}
            onChangePage={({ page }) => {
              const queryString = generateQueryString(searchKeywordsRef?.current || [])
              router.push(
                APP_ROUTES.POSTS.path + `?page=${page}${queryString ? `&${queryString}` : ''}`,
              )
            }}
          ></PaginationWithQuery>
        </div>
      )}
    </>
  )
}
