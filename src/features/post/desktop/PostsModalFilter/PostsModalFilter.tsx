'use client'
import { useTranslations } from 'next-intl'
import Image from 'next/image'
import FilterIcon from '@/assets/icons/setting-4.svg'
import { useDialog } from '@/hooks/common/useDialog'
import PostsFilterClient from './PostsFilter.client'

interface PostsModalFilterProps {
  onFilter: (filter: { params: string[]; type: 'close' | 'filter' }) => void
  selectedCategories?: string[]
  selectedLanguage?: string
}

const PostsModalFilter: React.FC<PostsModalFilterProps> = ({
  onFilter,
  selectedCategories = [],
  selectedLanguage = '',
}) => {
  const { openDialog } = useDialog()
  const t = useTranslations()

  const handleCloseDialog = (
    params: { params: string[]; type: 'close' | 'filter' },
    close: () => void,
  ) => {
    onFilter(params)
    close()
  }

  const handleOpenFilter = () => {
    openDialog({
      children: ({ close }) => (
        <PostsFilterClient
          close={(params) => handleCloseDialog(params, close)}
          initialSelectedCategories={selectedCategories}
          initialSelectedLanguage={selectedLanguage}
        />
      ),
      variant: 'blank',
      wrapperClassName:
        'mobile-wrapper z-[1000] !max-w-[800px] !w-[686px] min-h-[328px] overflow-y-auto sm:min-w-0 rounded-xl',
    })
  }

  return (
    <div
      className="typo-body-6 flex cursor-pointer items-center gap-3 text-primary-500"
      onClick={handleOpenFilter}
    >
      {t('MES-481')}
      <Image alt="filter" src={FilterIcon} width={16} height={16} />
    </div>
  )
}

export default PostsModalFilter
