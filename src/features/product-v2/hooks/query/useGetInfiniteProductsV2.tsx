import { medicineService } from '@/services/medicine.service'
import { Params } from '@/types/http.type'
import { MedicineV2 } from '@/types/medicine.type'
import { InfiniteData, UseInfiniteQueryOptions, useInfiniteQuery } from '@tanstack/react-query'
import { PaginatedDocs } from 'payload'
import { productV2QueryKeys } from './queryKeys'
import { Product } from '@/payload-types'
import { productV2Service } from '../../services/product-v2.service'

type ProductQueryConfig = Omit<
  UseInfiniteQueryOptions<
    PaginatedDocs<Product>,
    Error,
    InfiniteData<PaginatedDocs<Product>>,
    PaginatedDocs<Product>,
    (string | Params)[]
  >,
  'queryFn' | 'queryKey' | 'getNextPageParam' | 'initialPageParam'
>

interface UseGetInfiniteProductsV2Props {
  params?: Params
  options?: RequestInit
  key?: string | number
  config?: ProductQueryConfig
}

export const useGetInfiniteProductsV2 = ({
  params = {},
  options = {},
  config = {},
}: UseGetInfiniteProductsV2Props = {}) => {
  const {
    isError: isGetProductsV2Error,
    isFetching: isGetProductsV2Loading,
    data: productsV2,
    fetchNextPage,
    hasNextPage,
    ...rest
  } = useInfiniteQuery({
    queryKey: [productV2QueryKeys['products'].base(), params],
    queryFn: async ({ pageParam = 1 }) => {
      return productV2Service.getProductsV2({
        params: {
          ...params,
          page: pageParam,
        },
        options,
      })
    },
    getNextPageParam: (lastPage) => lastPage?.nextPage,
    getPreviousPageParam: (lastPage) => lastPage?.prevPage,
    initialPageParam: 1,
    ...config,
  })

  return {
    isGetProductsV2Error,
    isGetProductsV2Loading,
    productsV2,
    fetchNextPage,
    hasNextPage,
    ...rest,
  }
}
