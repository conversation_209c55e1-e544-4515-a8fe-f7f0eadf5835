import { Params } from '@/types/http.type'
import { UseQueryOptions, useQuery } from '@tanstack/react-query'
import { PaginatedDocs } from 'payload'
import { productV2QueryKeys } from './queryKeys'
import { productV2Service } from '../../services/product-v2.service'
import { ProductAgeGroup } from '@/payload-types'

type ProductAgeGroupQueryConfig = Omit<
  UseQueryOptions<PaginatedDocs<ProductAgeGroup> | null, Error>,
  'queryFn' | 'queryKey'
>

interface UseGetProductAgeGroupProps {
  params?: Params
  options?: RequestInit
  key?: string | number
  config?: ProductAgeGroupQueryConfig
}

/**
 * Custom hook for fetching product age groups data
 * @param params - Query parameters for filtering and pagination
 * @param options - Request initialization options
 * @param key - Optional cache key
 * @param config - React Query configuration options
 * @returns Object containing product age groups data and loading states
 */
export const useGetProductAgeGroup = ({
  params = {},
  options = {},
  key,
  config = {},
}: UseGetProductAgeGroupProps = {}) => {
  const {
    isError: isGetProductAgeGroupError,
    isPending: isGetProductAgeGroupLoading,
    data: productAgeGroups,
    ...rest
  } = useQuery({
    queryKey: [productV2QueryKeys['product-age-groups'].base(), params, key],
    queryFn: async () => {
      return productV2Service.getProductAgeGroup({
        params,
        options,
      })
    },
    ...config,
  })

  return {
    isGetProductAgeGroupError,
    isGetProductAgeGroupLoading,
    productAgeGroups,
    ...rest,
  }
}
