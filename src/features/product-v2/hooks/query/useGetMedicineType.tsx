import { Params } from '@/types/http.type'
import { UseQueryOptions, useQuery } from '@tanstack/react-query'
import { PaginatedDocs } from 'payload'
import { medicineTypeKeys } from './queryKeys'
import { productV2Service } from '../../services/product-v2.service'
import { MedicineType } from '@/payload-types'

type MedicineTypeQueryConfig = Omit<
  UseQueryOptions<PaginatedDocs<MedicineType> | null, Error>,
  'queryFn' | 'queryKey'
>

interface UseGetMedicineTypeProps {
  params?: Params
  options?: RequestInit
  key?: string | number
  config?: MedicineTypeQueryConfig
}

/**
 * Custom hook for fetching product age groups data
 * @param params - Query parameters for filtering and pagination
 * @param options - Request initialization options
 * @param key - Optional cache key
 * @param config - React Query configuration options
 * @returns Object containing product age groups data and loading states
 */
export const useGetMedicineType = ({
  params = {},
  options = {},
  key,
  config = {},
}: UseGetMedicineTypeProps = {}) => {
  const {
    isError: isGetMedicineTypeError,
    isPending: isGetMedicineTypeLoading,
    data: medicineType,
    ...rest
  } = useQuery({
    queryKey: [medicineTypeKeys['medicineType'].base(), params, key],
    queryFn: async () => {
      return productV2Service.getMedicineType({
        params,
        options,
      })
    },
    ...config,
  })

  return {
    isGetMedicineTypeError,
    isGetMedicineTypeLoading,
    medicineType,
    ...rest,
  }
}
