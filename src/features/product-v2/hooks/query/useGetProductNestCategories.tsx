import { Params } from '@/types/http.type'
import { InfiniteData, UseInfiniteQueryOptions, useInfiniteQuery } from '@tanstack/react-query'
import { PaginatedDocs } from 'payload'
import { categoriesV2QueryKeys } from './queryKeys'
import { productV2Service } from '../../services/product-v2.service'
import { ProductCategory } from '@/payload-types'

type CategoriesQueryConfig = Omit<
  UseInfiniteQueryOptions<
    PaginatedDocs<ProductCategory>,
    Error,
    InfiniteData<PaginatedDocs<ProductCategory>>,
    PaginatedDocs<ProductCategory>,
    (string | Params)[]
  >,
  'queryFn' | 'queryKey' | 'getNextPageParam' | 'initialPageParam'
>

interface useGetProductNestCategoriesProps {
  categoryId: string
  params?: Params
  options?: RequestInit
  key?: string | number
  config?: CategoriesQueryConfig
}

export const useGetProductNestCategories = ({
  categoryId,
  params = {},
  options = {},
  config = {},
}: useGetProductNestCategoriesProps) => {
  const {
    isError: isGetCategoriesV2Error,
    isFetching: isGetProductsV2Loading,
    data: productNestCategories,
    fetchNextPage,
    hasNextPage,
    ...rest
  } = useInfiniteQuery({
    queryKey: [categoriesV2QueryKeys.categories.base(), categoryId, params],
    queryFn: async ({ pageParam = 1 }) => {
      return productV2Service.getProductNestedCategories({
        categoryId,
        params: {
          ...params,
          page: pageParam,
        },
        options,
      })
    },
    getNextPageParam: (lastPage) => lastPage?.nextPage,
    getPreviousPageParam: (lastPage) => lastPage?.prevPage,
    initialPageParam: 1,
    ...config,
  })

  return {
    isGetCategoriesV2Error,
    isGetProductsV2Loading,
    productNestCategories,
    fetchNextPage,
    hasNextPage,
    ...rest,
  }
}
