import { LocaleEnum } from '@/enums/locale.enum'
import { ProductV2TypeEnum } from '../enums'

export const PRODUCT_V2_TYPE_OPTIONS_BASE = {
  [ProductV2TypeEnum.MEDICINE]: {
    label: 'Medicine',
    value: ProductV2TypeEnum.MEDICINE,
    adminLabel: {
      [LocaleEnum.VI]: 'Thuốc',
      [LocaleEnum.JA]: '薬',
    },
    translationKey: 'MES-718',
  },
  [ProductV2TypeEnum.DIETARY_SUPPLEMENT]: {
    label: 'Dietary Supplement',
    value: ProductV2TypeEnum.DIETARY_SUPPLEMENT,
    adminLabel: {
      [LocaleEnum.VI]: 'Thức phẩm chức năng',
      [LocaleEnum.JA]: '栄養補助食品',
    },
    translationKey: 'MES-471',
  },
  [ProductV2TypeEnum.MEDICAL_INSTRUMENT]: {
    label: 'Medical Instrument',
    value: ProductV2TypeEnum.MEDICAL_INSTRUMENT,
    adminLabel: {
      [LocaleEnum.VI]: 'Dụng cụ y khoa',
      [LocaleEnum.JA]: '医療機器',
    },
    translationKey: 'MES-705',
  },
}
