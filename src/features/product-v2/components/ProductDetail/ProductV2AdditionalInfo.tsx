'use client'
import bookMarkIcon from '@/assets/icons/book-mark-icon.svg'
import profile2userIcon from '@/assets/icons/profile-2user-icon.svg'
import { Media, MedicineType, Product, ProductAgeGroup, ProductCategory } from '@/payload-types'
import { useTranslations } from 'next-intl'
import Image from 'next/image'
import { useMemo } from 'react'
import { PRODUCT_V2_TYPE_OPTIONS } from '../../constants'
interface ProductV2AdditionalInfoProps {
  product: Product
}
export const ProductV2AdditionalInfo = ({ product }: ProductV2AdditionalInfoProps) => {
  const t = useTranslations()
  const { ageGroups, type, categories, medicineType } = product

  // Filter out items that are not proper objects
  const validCategories = useMemo(
    () =>
      categories?.filter(
        (category) => typeof category === 'object' && category !== null && 'title' in category,
      ) as ProductCategory[],
    [categories],
  )

  const categoriesSummary = validCategories?.map((category) => category.title).join(' ・ ')

  // Filter out items that are not proper objects
  const validAgeGroups = useMemo(
    () =>
      ageGroups?.filter(
        (ageGroup) =>
          typeof ageGroup === 'object' &&
          ageGroup !== null &&
          'title' in ageGroup &&
          'id' in ageGroup,
      ) as ProductAgeGroup[],
    [ageGroups],
  )

  return (
    <div className="space-y-4 rounded-lg bg-custom-background-hover p-3">
      {/* Age Groups */}
      {validAgeGroups && validAgeGroups.length > 0 && (
        <div className="flex items-start gap-2">
          <Image
            src={profile2userIcon}
            alt="profile-2user-icon"
            width={20}
            height={20}
            className="size-5"
          />
          <div className="space-y-2">
            {validAgeGroups.map((ageGroup) => {
              if (typeof ageGroup !== 'object' || ageGroup === null) return null
              const { title, id } = ageGroup

              return (
                <div className="typo-body-7" key={id}>
                  {title}
                </div>
              )
            })}
          </div>
        </div>
      )}

      {/* Type */}
      {type && (
        <div className="flex items-start gap-2">
          <Image
            src={PRODUCT_V2_TYPE_OPTIONS[type]?.icon}
            alt="product-v2-icon"
            width={20}
            height={20}
            className="size-5"
          />
          <div className="typo-body-7">{t(PRODUCT_V2_TYPE_OPTIONS[type]?.translationKey)}</div>
        </div>
      )}

      {/* Medicine Type */}
      {medicineType && medicineType?.length > 0 && (
        <div className="flex flex-col gap-2">
          {medicineType.map((item) => {
            if (typeof item !== 'object' || item === null) return null
            const { title, id, name, icon, note } = item as MedicineType
            const { url, thumbnailURL } = (icon as Media) || {}
            const iconUrl = url || thumbnailURL || ''
            return (
              <div className="flex items-start gap-2" key={id}>
                {iconUrl && (
                  <Image
                    src={iconUrl}
                    alt="product-v2-icon"
                    width={20}
                    height={20}
                    className="size-5"
                  />
                )}

                <div className="space-y-1">
                  <p className="typo-body-7">{name || title}</p>
                  <p className="typo-body-9 text-subdued">{note}</p>
                </div>
              </div>
            )
          })}
        </div>
      )}

      {/* Categories */}
      {categoriesSummary && (
        <div className="flex flex-1 items-start gap-2">
          <Image
            src={bookMarkIcon}
            alt="profile-2user-icon"
            width={20}
            height={20}
            className="size-5"
          />
          <div className="typo-body-7">{categoriesSummary}</div>
        </div>
      )}
    </div>
  )
}
