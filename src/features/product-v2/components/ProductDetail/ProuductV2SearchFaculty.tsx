'use client'
import messageQuestion from '@/assets/icons/message-question.svg'
import searchIconPrimary from '@/assets/icons/search-icon-primary.svg'
import { useWebViewMessaging } from '@/hooks/common/useWebViewMessaging'
import { APP_ROUTES } from '@/routes'
import { NATIVE_APP_ROUTES } from '@/routes/nativeAppRoutes'
import { useTranslations } from 'next-intl'
import Image from 'next/image'
import Link from 'next/link'
interface ProuductV2SearchFacultyProps {
  isNative?: boolean
}
export const ProuductV2SearchFaculty = ({ isNative }: ProuductV2SearchFacultyProps) => {
  const { redirectToScreen } = useWebViewMessaging()
  const t = useTranslations()
  return (
    <div className="flex gap-3 rounded-[6px] bg-informative-200 p-3">
      <Image
        src={messageQuestion}
        alt="message-icon"
        width={18}
        height={18}
        className="size-[18px]"
      ></Image>
      <div className="flex flex-col gap-1">
        <div className="typo-body-6">{t('MES-488')}</div>
        <div className="typo-body-9">{t('MES-489')}</div>

        <Link
          href={APP_ROUTES.MEDICAL_HANDBOOK?.children?.FACULTIES?.path}
          className="flex items-center gap-2 py-2"
          onClick={(e) => {
            if (isNative) {
              e.preventDefault()
              redirectToScreen(NATIVE_APP_ROUTES.MEDICAL_HANDBOOK.tabPath!)
              return
            }
          }}
        >
          <div className="typo-link-3 text-primary">{t('MES-35')}</div>
          <Image
            src={searchIconPrimary}
            alt="arrow-icon"
            width={14}
            height={14}
            className="size-4"
          ></Image>
        </Link>
      </div>
    </div>
  )
}
