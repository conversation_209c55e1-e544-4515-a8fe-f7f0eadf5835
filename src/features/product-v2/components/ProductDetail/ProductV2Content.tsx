'use client'
import arrowDown from '@/assets/icons/arrow-down.svg'
import arrowUp from '@/assets/icons/arrow-up.svg'
import RichText from '@/components/RichText'
import { isRichTextEmpty } from '@/utilities/isRichTextEmpty'
import { SerializedEditorState } from '@payloadcms/richtext-lexical/lexical'
import { useTranslations } from 'next-intl'
import Image from 'next/image'
import React, { useState } from 'react'

export const ProductV2Content: React.FC<{
  uses: SerializedEditorState
  dosage: SerializedEditorState
  dosageForm: SerializedEditorState
  specification: SerializedEditorState
  ingredient: SerializedEditorState
  contraindications: SerializedEditorState
  note: SerializedEditorState
  isShowFull?: boolean
  isHiddenButtonShow?: boolean
}> = ({
  uses,
  dosage,
  dosageForm,
  specification,
  ingredient,
  contraindications,
  note,
  isShowFull = false,
  isHiddenButtonShow = false,
}) => {
  const [isSeeFull, setIsSeeFull] = useState<boolean>(() => isShowFull)

  const t = useTranslations()

  const isShowAdsense = true
  return (
    <>
      {/* Uses */}
      {uses && !isRichTextEmpty(uses) && (
        <div className="flex flex-col gap-y-2">
          <h5 className="typo-body-6 text-primary">{t('MES-632')}:</h5>
          <RichText
            className="content-section"
            enableProse={true}
            data={uses}
            enableGutter={false}
            showADS={isShowAdsense}
          />
        </div>
      )}

      {/* Dosage Form */}
      {dosageForm && !isRichTextEmpty(dosageForm) && (
        <div className="flex flex-col gap-y-2">
          <h5 className="typo-body-6 text-primary">{t('MES-291')}:</h5>
          <RichText
            className="content-section"
            enableProse={true}
            data={dosageForm}
            enableGutter={false}
            showADS={isShowAdsense}
          />
        </div>
      )}

      {/* Specification */}
      {specification && !isRichTextEmpty(specification) && (
        <div className="flex flex-col gap-y-2">
          <h5 className="typo-body-6 text-primary">{t('MES-292')}:</h5>
          <RichText
            className="content-section"
            enableProse={true}
            data={specification}
            enableGutter={false}
            showADS={isShowAdsense}
          />
        </div>
      )}

      {/* Ingredient */}
      {ingredient && !isRichTextEmpty(ingredient) && (
        <div className="flex flex-col gap-y-2">
          <h5 className="typo-body-6 text-primary">{t('MES-90')}:</h5>
          <RichText
            className="content-section"
            enableProse={true}
            data={ingredient}
            enableGutter={false}
            showADS={isShowAdsense}
          />
        </div>
      )}

      {/* Toggle content */}
      {isSeeFull && (
        <div className="flex flex-col gap-3 pb-3">
          {/* Dosage */}
          {dosage && !isRichTextEmpty(dosage) && (
            <div className="flex flex-col gap-y-2">
              <h5 className="typo-body-6 text-primary">{t('MES-88')}:</h5>
              <RichText
                className="content-section"
                enableProse={true}
                data={dosage}
                enableGutter={false}
                showADS={isShowAdsense}
              />
            </div>
          )}
          {/* Contraindications */}
          {contraindications && !isRichTextEmpty(contraindications) && (
            <div className="flex flex-col gap-y-2">
              <h5 className="typo-body-6 text-danger-700">{t('MES-89')}:</h5>
              <RichText
                className="content-section"
                enableProse={true}
                data={contraindications}
                enableGutter={false}
                showADS={isShowAdsense}
              />
            </div>
          )}
          {/* Note */}
          {note && !isRichTextEmpty(note) && (
            <div className="flex flex-col gap-y-2">
              <h5 className="typo-body-6 text-danger-700">{t('MES-293')}:</h5>
              <RichText
                className="content-section"
                enableProse={true}
                data={note}
                enableGutter={false}
                showADS={isShowAdsense}
              />
            </div>
          )}
        </div>
      )}

      {/* Toggle button - only show if there's toggleable content */}
      {!isHiddenButtonShow &&
        ((dosage && !isRichTextEmpty(dosage)) ||
        (contraindications && !isRichTextEmpty(contraindications)) ||
        (note && !isRichTextEmpty(note)) ? (
          <div
            onClick={() => setIsSeeFull(!isSeeFull)}
            className="typo-link-3 flex cursor-pointer items-center justify-center gap-2"
          >
            <div className="typo-link-3 text-primary">{t(isSeeFull ? 'MES-493' : 'MES-22')}</div>
            <Image
              src={isSeeFull ? arrowUp : arrowDown}
              alt="arrow-icon"
              width={15}
              height={15}
            ></Image>
          </div>
        ) : null)}
    </>
  )
}
