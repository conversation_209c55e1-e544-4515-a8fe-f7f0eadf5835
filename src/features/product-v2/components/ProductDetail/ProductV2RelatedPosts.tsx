'use client'
import React from 'react'
import { Swiper, SwiperSlide } from 'swiper/react'
import { PostCardItem } from '../../../post/components/PostCardItem/PostCardItem'
import { useGetPosts } from '@/hooks/query/post/useGetPosts'
import { useAppLanguage } from '@/contexts/AppLanguageContext/AppLanguageContext'
import Link from 'next/link'
import { MESSAGE_NATIVE } from '@/constants/message-native.constant'
import { useDetectNativeEnvironment } from '@/hooks/native/useDetectNativeEnviroment'
import { Media, PostCategory } from '@/payload-types'
import { useWebViewMessaging } from '@/hooks/common/useWebViewMessaging'
import { cn } from '@/utilities/cn'
import Image from 'next/image'
import { dateToYMD } from '@/utilities/date'
import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { useTranslations } from 'next-intl'

export const ProductV2RelatedPosts = () => {
  const { primaryLanguage } = useAppLanguage()
  const { sendMessage } = useWebViewMessaging()
  const { isNative } = useDetectNativeEnvironment()
  const { posts, isGetPostsLoading } = useGetPosts({
    params: {
      limit: 10,
      sort: '-createdAt',
      locale: primaryLanguage,
      where: {
        and: [
          {
            language: { equals: primaryLanguage || 'vi' },
          },
        ],
      },
    },
    useQueryOptions: {
      staleTime: 5 * 60 * 1000,
    },
  })
  const t = useTranslations()
  if (isGetPostsLoading) {
    return (
      <div className="space-y-3">
        <Swiper spaceBetween={17} slidesPerView={1.09}>
          {new Array(10).fill(0).map((_, index) => {
            return (
              <SwiperSlide key={index}>
                <div className="flex flex-col gap-2">
                  <Skeleton className="h-[197px] w-full" />
                  <Skeleton className="h-[16px] w-2/3" />
                  <Skeleton className="h-[16px] w-1/3" />
                </div>
              </SwiperSlide>
            )
          })}
        </Swiper>
      </div>
    )
  }

  return (
    <div className="space-y-3">
      <h3 className="typo-body-3">{t('MES-23')}</h3>
      <Swiper spaceBetween={17} slidesPerView={posts && posts?.docs?.length > 1 ? 1.09 : 1}>
        {posts &&
          posts?.docs?.map((post) => {
            const { id, heroImage, title, categories, createdAt, slug } = post
            const { url, thumbnailURL } = (heroImage as Media) || {}
            const imageUrl = thumbnailURL || url

            const { title: categoryTitle } = (categories as PostCategory) || {}
            return (
              <SwiperSlide key={post.id}>
                <Link
                  onClick={(e) => {
                    if (isNative) {
                      e.preventDefault()
                      sendMessage(MESSAGE_NATIVE.openPostDetail, {
                        slug: post.slug,
                      })
                    }
                  }}
                  href={`/posts/${slug}`}
                  key={id}
                  className={cn('relative flex flex-col gap-3')}
                >
                  {/* Image */}
                  <div
                    className={cn(
                      'relative overflow-hidden rounded-lg bg-neutral-100',
                      'aspect-video w-full',
                    )}
                  >
                    {imageUrl && (
                      <Image
                        src={imageUrl}
                        alt={title}
                        fill
                        className="h-full w-full object-cover"
                        sizes="360px"
                      ></Image>
                    )}
                  </div>

                  {/* Info */}

                  <div className="flex-1 space-y-2">
                    <div className="typo-body-8 min-h-5 w-fit rounded-[4px] bg-primary-50 px-2 py-1 text-center text-primary">
                      {categoryTitle}
                    </div>
                    <time className="typo-body-9 block">{dateToYMD(createdAt)}</time>

                    <h3 className={cn('line-clamp-2', 'typo-body-6 !font-medium')}>{title}</h3>
                  </div>
                </Link>
              </SwiperSlide>
            )
          })}
      </Swiper>
    </div>
  )
}
