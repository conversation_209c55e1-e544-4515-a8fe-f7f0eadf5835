'use client'
import Image from 'next/image'

import ResearchByImageForDesktop from '@/assets/images/researchByImageDesktop.png'
import { useTranslations } from 'next-intl'
import Link from 'next/link'
import { Button } from '@/components/ui/Button/Button'

import QR from '@/assets/images/qr-app.png'
import AppleIcon from '@/assets/icons/apple-icon.svg'
import CloseIcon from '@/assets/icons/exit.svg'

type ResearchByImageProps = {
  onClose: () => void
}
const ResearchByImage: React.FC<ResearchByImageProps> = ({ onClose }) => {
  const t = useTranslations()
  return (
    <div className="relative flex h-full w-full items-end gap-3 bg-white px-[37px] py-8">
      <Button onClick={onClose} variant={'blank'} className="absolute right-4 top-4 p-0">
        <Image src={CloseIcon} alt={'close'} height={24} width={24} />
      </Button>

      <div className="relative flex flex-[1.5] shrink-0 items-center justify-center">
        <Image
          src={ResearchByImageForDesktop}
          alt="research-by-image"
          className="h-full w-full object-cover"
        />
      </div>

      <div className="flex flex-[3] flex-col items-center justify-center gap-4 text-center">
        <div className="typo-heading-7 py-2 text-primary-500">{t('MES-791')}</div>
        <div className="typo-body-7 text-center">{t('MES-792')}</div>
        <div className="flex items-center justify-center gap-3">
          <Image src={QR} height={120} width={120} alt="qr" />

          <Link
            href=""
            className="flex items-center justify-center gap-3 rounded-lg bg-black px-4 py-2 text-white"
          >
            <Image src={AppleIcon} alt="apple" height={17} width={17} />
            <span className="typo-body-8 m-0 p-0 text-white">{t('MES-732')}</span>
          </Link>
        </div>
      </div>
    </div>
  )
}

export default ResearchByImage
