'use client'

import { useTranslations } from 'next-intl'
import { useEffect, useRef, useState } from 'react'

// image
import { PRODUCT_V2_TYPE_OPTIONS } from '@/features/product-v2/constants'
import { ProductV2TypeEnum } from '@/features/product-v2/enums'
import { useSearchQuery } from '@/hooks/common/useSearchQuery'
import { APP_ROUTES } from '@/routes'
import { useDebounce } from '@/utilities/useDebounce'
import { useRouter } from 'next/navigation'
import SearchProduct from '../common/SearchProduct'

const MedicineHeader: React.FC = ({}) => {
  const t = useTranslations()
  const isInitializedRef = useRef<boolean>(false)

  const router = useRouter()

  const { updateSearchQuery, getAllSearchQueries } = useSearchQuery()

  const [inputValue, setInputValue] = useState<string>('')

  const [selectedCategories, setSelectedCategories] = useState<ProductV2TypeEnum>(
    ProductV2TypeEnum.MEDICINE,
  )

  const debouncedSearchValue = useDebounce(inputValue, 500)

  const { category } = getAllSearchQueries()
  // Initialize category from URL on component mount
  useEffect(() => {
    if (category && Object.values(ProductV2TypeEnum).includes(category as ProductV2TypeEnum)) {
      setSelectedCategories(category as ProductV2TypeEnum)
    }
  }, [category])

  useEffect(() => {
    if (!isInitializedRef.current) {
      return
    }

    router.push(`${APP_ROUTES.PRODUCTS_V2.path}/search?q=${debouncedSearchValue}`)
  }, [debouncedSearchValue, router])

  const handleInputChange = (data: string) => {
    isInitializedRef.current = true

    setInputValue(data)
  }

  const handleSelectCategory = (category: ProductV2TypeEnum) => {
    if (selectedCategories === category) return
    setSelectedCategories(category)
    updateSearchQuery({ category }, 'replace', true, { reset: true })
  }

  return (
    <>
      <div className="flex items-center justify-between gap-3">
        <div className="typo-heading-7 text-primary-500">{t('MES-561')}</div>

        <SearchProduct value="" onChange={handleInputChange} />
      </div>

      <div className="mt-3 flex items-center gap-3">
        {Object.values(PRODUCT_V2_TYPE_OPTIONS).map((product) => (
          <div
            onClick={() => handleSelectCategory(product.value)}
            key={product.label}
            className={`typo-body-6 cursor-pointer rounded-xl border ${product.value === selectedCategories ? 'border-primary-500 bg-white text-primary-500' : 'border-transparent bg-neutral-50 text-subdued'} px-3 py-[6px]`}
          >
            {t(product.translationKey)}
          </div>
        ))}
      </div>
    </>
  )
}

export default MedicineHeader
