'use client'
import { Spinner } from '@/components/ui/Loading/Spinner'
import { APP_ROUTES } from '@/routes'
import { useDebounce } from '@/utilities/useDebounce'
import { useLocale } from 'next-intl'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import InfiniteScroll from 'react-infinite-scroll-component'
import { useGetInfiniteProductsV2 } from '../../hooks/query/useGetInfiniteProductsV2'
import SearchProduct from '../common/SearchProduct'
import ProductSearchList from '../components/products/ProductSearchList'

type MedicineSearchWrapperProps = {
  paramsSearch: {
    [key: string]: string | undefined
  }
}
const MedicineSearchWrapper: React.FC<MedicineSearchWrapperProps> = ({ paramsSearch }) => {
  const locale = useLocale()

  const router = useRouter()

  const { q: query } = paramsSearch

  const [searchValue, setSearchValue] = useState<string>(query ?? '')

  const debounce = useDebounce(searchValue, 500)

  useEffect(() => {
    router.push(`${APP_ROUTES.PRODUCTS_V2.path}/search?q=${debounce}`)
  }, [debounce, router])

  const [filterOptions, setFilterOptions] = useState<any>([])

  const handleChange = (value: string) => {
    setSearchValue(value)
  }

  const {
    isLoading: isProductListLoading,
    productsV2: productList,
    fetchNextPage,
    hasNextPage,
  } = useGetInfiniteProductsV2({
    config: {
      staleTime: 5 * 60 * 1000,
    },
    params: {
      locale: locale ?? 'vi',
      limit: 30,
      categories:
        filterOptions.filter((item: any) => item.type === 'category').map((item: any) => item.id) ||
        undefined,
      where: {
        and: [
          {
            or: [
              {
                title: {
                  like: query ? query : undefined,
                },
              },
              {
                'keywords.name': {
                  like: query ? query : undefined,
                },
              },
              {
                jaTitle: {
                  like: query ? query : undefined,
                },
              },
            ],
          },
          {
            type: {
              in: filterOptions
                .filter((item: any) => item.type === 'productType')
                .map((item: any) => item.value),
            },
            ageGroups: {
              in: filterOptions
                .filter((item: any) => item.type === 'age')
                .map((item: any) => item.id),
            },
            medicineType: {
              in: filterOptions
                .filter((item: any) => item.type === 'medicine')
                .map((item: any) => item.id),
            },
          },
        ],
      },
    },
  })

  const allProductListData = productList?.pages.flatMap((page) => page.docs) ?? []

  const applyFilter = (data: any) => {
    const { params } = data
    console.log(params)
    setFilterOptions(params)
  }

  return (
    <div className="h-full w-full max-w-[calc(100vw-320px)] bg-custom-background-hover px-16 py-6">
      <div className="w-full rounded-xl bg-white p-4">
        <SearchProduct value={searchValue} onChange={handleChange} />

        {/* Product List with Infinite Scroll */}
        {isProductListLoading || allProductListData.length === 0 ? (
          <ProductSearchList
            totalData={productList?.pages[0]?.totalDocs ?? 0}
            isLoading={isProductListLoading}
            productListData={[]}
            applyFilter={applyFilter}
            filterOptions={filterOptions}
          />
        ) : (
          productList &&
          productList.pages[0]?.docs.length > 0 && (
            <InfiniteScroll
              dataLength={allProductListData.length}
              next={() => {
                if (hasNextPage) {
                  fetchNextPage()
                }
              }}
              hasMore={!!hasNextPage}
              loader={
                <div className="flex items-center justify-center py-4">
                  <Spinner />
                </div>
              }
              scrollThreshold={0.8}
            >
              <ProductSearchList
                totalData={productList?.pages[0]?.totalDocs ?? 0}
                isLoading={false}
                productListData={allProductListData}
                filterOptions={filterOptions}
                applyFilter={applyFilter}
              />
            </InfiniteScroll>
          )
        )}
      </div>
    </div>
  )
}

export default MedicineSearchWrapper
