import { Skeleton } from '@/components/ui/Skeleton/Skeleton'

const ProductDetailSkeleton: React.FC = () => {
  return (
    <div className="h-full w-full max-w-[calc(100vw-320px)] bg-custom-background-hover px-16 py-6">
      <div className="flex w-full gap-6">
        {/* Left column - ProductDetailCommon skeleton */}
        <div className="h-fit flex-[3] shrink-0 rounded-xl bg-white p-4">
          <div className="flex w-full flex-col gap-3">
            {/* Back button skeleton */}
            <div className="flex items-center gap-3">
              <Skeleton className="h-5 w-5" />
              <Skeleton className="h-4 w-20" />
            </div>

            {/* Image skeleton */}
            <Skeleton className="h-[232px] w-[325px]" />

            {/* Disclaimer warning skeleton */}
            <div className="rounded-lg bg-amber-50 p-3">
              <div className="flex items-start gap-2">
                <Skeleton className="h-5 w-5 shrink-0" />
                <div className="flex flex-col gap-2">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-3 w-full" />
                  <Skeleton className="h-3 w-3/4" />
                </div>
              </div>
            </div>

            {/* Additional info skeleton */}
            <div className="flex flex-col gap-3">
              <Skeleton className="h-6 w-40" />
              <div className="space-y-2">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-2/3" />
              </div>
            </div>

            {/* Search faculty skeleton */}
            <div className="flex flex-col gap-2">
              <Skeleton className="h-5 w-32" />
              <Skeleton className="h-10 w-full" />
            </div>
          </div>
        </div>

        {/* Middle column - ProductDetailContent skeleton */}
        <div className="h-fit flex-[4.5] shrink-0 rounded-xl bg-white p-4">
          <div className="flex w-full flex-col gap-6">
            {/* Title and favorite button skeleton */}
            <div className="flex items-start justify-between gap-4">
              <div className="flex-1">
                <Skeleton className="h-7 w-full mb-2" />
                <Skeleton className="h-7 w-3/4" />
              </div>
              <Skeleton className="h-8 w-8 shrink-0" />
            </div>

            {/* Japanese title skeleton */}
            <Skeleton className="h-5 w-2/3" />

            {/* Content sections skeleton */}
            <div className="flex flex-col gap-3 rounded-lg bg-white p-3">
              <div className="flex items-center gap-3">
                <Skeleton className="h-5 w-5 shrink-0" />
                <Skeleton className="h-4 w-24" />
              </div>

              {/* Multiple content sections */}
              {Array.from({ length: 6 }).map((_, index) => (
                <div key={index} className="space-y-2">
                  <Skeleton className="h-5 w-32" />
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Right column - ProductDetailStore skeleton */}
        <div className="h-fit flex-[2.5] shrink-0 rounded-xl bg-white p-4">
          <div className="flex flex-col gap-4">
            {/* Store title skeleton */}
            <Skeleton className="h-6 w-32" />

            {/* Store items skeleton */}
            {Array.from({ length: 3 }).map((_, index) => (
              <div key={index} className="flex items-center gap-3 rounded-lg border p-3">
                <Skeleton className="h-12 w-12 shrink-0" />
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-3 w-2/3" />
                </div>
                <Skeleton className="h-8 w-16 shrink-0" />
              </div>
            ))}

            {/* Additional store info skeleton */}
            <div className="space-y-2">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ProductDetailSkeleton
