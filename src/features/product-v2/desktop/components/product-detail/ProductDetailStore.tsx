import { MedStore } from '@/features/product-v2/components/ProductDetail/ProductV2Store'
import { Media, MedicineBuyButton, Product } from '@/payload-types'
import { useTranslations } from 'next-intl'
import Image from 'next/image'
import React from 'react'

import ExportPrimaryIcon from '@/assets/icons/export-primary.svg'

type ProductDetailStoreProps = {
  product: Product
}

const ProductDetailStore: React.FC<ProductDetailStoreProps> = ({ product }) => {
  const t = useTranslations()
  const { stores } = product
  const filteredStores = (stores as MedStore[]).filter((store) => {
    const medicineStore = store?.['medicine-store'] as unknown as MedicineBuyButton
    return store.url && medicineStore?.id && medicineStore?.title
  })

  return (
    <div className="flex flex-col gap-3">
      <div className="typo-body-7 text-subdued">{t('MES-714')}</div>

      <div className="flex flex-col gap-2">
        {filteredStores.map((store) => {
          const { url, id } = store
          const medicineStore = (store?.['medicine-store'] as unknown as MedicineBuyButton) || []
          const { title, logo } = medicineStore
          const { thumbnailURL, url: logoUrl } = (logo as Media) || {}
          const iconStoreUrl = thumbnailURL || logoUrl

          return (
            <React.Fragment key={id}>
              {title ? (
                <a
                  key={id}
                  href={url!}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center justify-between gap-2 rounded-lg bg-neutral-50 px-4 py-2"
                >
                  <div className="flex items-center gap-2">
                    {iconStoreUrl && (
                      <Image
                        src={iconStoreUrl}
                        alt={title}
                        width={16}
                        height={16}
                        className="size-4 shrink-0 object-contain"
                        key={id}
                      ></Image>
                    )}

                    <span className="typo-body-8 truncate">{title}</span>
                  </div>

                  <Image alt="link" src={ExportPrimaryIcon} className="size-5" />
                </a>
              ) : (
                <></>
              )}
            </React.Fragment>
          )
        })}
      </div>
    </div>
  )
}

export default ProductDetailStore
