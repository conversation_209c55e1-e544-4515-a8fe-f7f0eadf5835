import Image from 'next/image'

import ArrowLeftIcon from '@/assets/icons/arrow-right.svg'
import { DisclaimerWarning } from '@/components/DisclaimerWarning/DisclaimerWarning'
import { ProductV2AdditionalInfo } from '@/features/product-v2/components/ProductDetail/ProductV2AdditionalInfo'
import { Product } from '@/payload-types'
import { APP_ROUTES } from '@/routes'
import { useTranslations } from 'next-intl'
import Link from 'next/link'

type ProductDetailCommon = {
  thumbnailURL: string | null | undefined
  product: Product
}

const ProductDetailCommon: React.FC<ProductDetailCommon> = ({ thumbnailURL, product }) => {
  const t = useTranslations()

  return (
    <div className="flex w-full flex-col gap-3">
      <Link
        href={APP_ROUTES.PRODUCTS_V2.path}
        className="typo-button-3 flex items-center gap-3 text-subdued"
      >
        <Image className="size-5 rotate-180" src={ArrowLeftIcon} alt={'arrow-left'} />
        {t('MES-77')}
      </Link>

      {thumbnailURL && (
        <div className="relative h-[232px] w-[325px]">
          <Image src={thumbnailURL} fill alt="thumbnail" className="h-full w-full object-cover" />
        </div>
      )}

      <DisclaimerWarning></DisclaimerWarning>

      <ProductV2AdditionalInfo product={product} />

      {/* <ProuductV2SearchFaculty isNative={false} /> */}
    </div>
  )
}

export default ProductDetailCommon
