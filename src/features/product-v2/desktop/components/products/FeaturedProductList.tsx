import { useTranslations } from 'next-intl'
import Image from 'next/image'

import FeaturedIcon from '@/assets/icons/featured.svg'
import { Product } from '@/payload-types'
import { Swiper, SwiperSlide } from 'swiper/react'
import ProductItem from './ProductItem'
import ProductSkeleton from './ProductSkeleton'

type FeatureProductType = {
  featureProduct: Product[]
  isLoading: boolean
}

const FeaturedProductList: React.FC<FeatureProductType> = ({ featureProduct, isLoading }) => {
  const t = useTranslations()
  return isLoading ? (
    <ProductSkeleton />
  ) : (
    !!featureProduct.length && (
      <>
        <div className="typo-body-3 mt-4 flex items-center gap-3">
          {t('MES-69')}
          <Image src={FeaturedIcon} alt={'featured'} height={24} width={24} />
        </div>

        <div className="mt-3">
          <Swiper spaceBetween={12} slidesPerView={6.5}>
            {featureProduct.map((product) => {
              return (
                <SwiperSlide key={product.id}>
                  <ProductItem isNew={true} productData={product} />
                </SwiperSlide>
              )
            })}
          </Swiper>
        </div>
      </>
    )
  )
}

export default FeaturedProductList
