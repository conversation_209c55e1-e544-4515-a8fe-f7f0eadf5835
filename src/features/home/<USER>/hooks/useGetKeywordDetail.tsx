import { Keyword } from '@/payload-types'
import { keywordService } from '@/services/keyword.service'
import { Params } from '@/types/http.type'
import { useQuery, UseQueryOptions } from '@tanstack/react-query'
import { keywordKeys } from './queryKeys'

type KeywordRandomProps = Omit<UseQueryOptions<Keyword | null, Error>, 'queryFn' | 'queryKey'>

interface IUseGetKeywordDetail {
  params?: Params
  options?: RequestInit
  key?: string | number
  config?: KeywordRandomProps
  id: string
}

export const useGetKeywordDetail = ({
  id,
  params = {},
  options = {},
  key,
  config = {},
}: IUseGetKeywordDetail) => {
  const {
    isError: isGetKeywordError,
    isPending: isGetKeywordLoading,
    data: keyword,
    ...rest
  } = useQuery({
    queryKey: [keywordKeys['keyword-detail'].base(), params, key],
    queryFn: async () =>
      keywordService.getKeywordsDetail({
        id,
        params: params,
        options: options,
      }),
    ...config,
  })

  return {
    isGetKeywordError,
    isGetKeywordLoading,
    keyword,
    ...rest,
  }
}
