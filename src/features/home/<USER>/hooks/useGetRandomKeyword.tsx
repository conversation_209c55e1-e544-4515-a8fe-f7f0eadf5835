import { Keyword } from '@/payload-types'
import { keywordService } from '@/services/keyword.service'
import { Params } from '@/types/http.type'
import { useQuery, UseQueryOptions } from '@tanstack/react-query'
import { PaginatedDocs } from 'payload'
import { keywordKeys } from './queryKeys'

type KeywordRandomProps = Omit<UseQueryOptions<PaginatedDocs<Keyword | null>, Error>, 'queryFn' | 'queryKey'>

interface IUseGetRandomKeyword {
  params?: Params
  options?: RequestInit
  key?: string | number
  config?: KeywordRandomProps
}

export const useGetRandomKeyword = ({
  params = {},
  options = {},
  key,
  config = {},
}: IUseGetRandomKeyword = {}) => {
  const {
    isError: isGetKeywordError,
    isPending: isGetKeywordLoading,
    data: keywords,
    ...rest
  } = useQuery({
    queryKey: [keywordKeys['keywords'].base(), params, key],
    queryFn: async () =>
      keywordService.getKeywords({
        params: params,
        options: options,
      }),
    ...config,
  })

  return {
    isGetKeywordError,
    isGetKeywordLoading,
    keywords,
    ...rest,
  }
}
