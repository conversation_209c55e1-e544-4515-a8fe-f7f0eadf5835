'use client'
import audioIcon from '@/assets/icons/audio-icon.svg'

import { Spinner } from '@/components/ui/Loading/Spinner'
// import { Spinner } from '@/components/ui/Loading/Spinner'
import { LocaleEnum } from '@/enums/locale.enum'
import { Section } from '@/features/home/<USER>/Section/Section'
import { useDialog } from '@/hooks/common/useDialog'
import { useGetKeywordAudio } from '@/hooks/query/keyword/useGetKeywordAudio'
import { Keyword, Media } from '@/payload-types'
import { LocalizeField } from '@/types/custom-payload.type'
import { KeywordV2 } from '@/types/keyword.type'
import { useTranslations } from 'next-intl'
import dynamic from 'next/dynamic'
import Image from 'next/image'
import { useMemo, useRef, useState } from 'react'

import KeywordIcon from '@/assets/icons/keyword-icon-v2.svg'
const KeywordDetailsPopup = dynamic(
  () =>
    import('@/components/Popup/KeywordDetailsPopup/KeywordDetailsPopup').then(
      (mod) => mod.KeywordDetailsPopup,
    ),
  {
    ssr: false,
  },
)
interface HomeDailyVocabularyProps {
  keywords: Keyword[]
}

export const HomeDailyVocabulary: React.FC<HomeDailyVocabularyProps> = ({ keywords }) => {
  const t = useTranslations()

  const keywordList = useMemo(() => {
    return keywords ? keywords : []
  }, [keywords])

  const { openDialog } = useDialog()
  // Cache for storing audio URLs from API calls
  const audioCache = useRef<Record<string, string>>({})
  const { getKeywordAudioMutation } = useGetKeywordAudio()
  const [loadingItemId, setLoadingItemId] = useState<string | null>(null)
  // Use useRef instead of useState for favorite keywords
  const favoriteKeywordsRef = useRef<Record<string, boolean>>({})

  // Handle favorite keyword update
  const handleUpdateFavoriteKeywordSuccess = (keywordId: string, isFavorite: boolean) => {
    favoriteKeywordsRef.current[keywordId] = isFavorite
  }

  const handlePlayAudio = (item: Keyword) => {
    if (!item.id) return

    // Check cached audio URL first without setting loading state
    if (audioCache.current[item.id]) {
      const audio = new Audio(audioCache.current[item.id])
      audio.play().catch((error) => {
        console.error('Error playing cached audio:', error)
      })
      return
    }

    // Check if the keyword has audio files without setting loading state
    if (item.audio && item.audio.length > 0) {
      // Find audio JA
      const audioItem = item.audio.find((audio) => audio.language === LocaleEnum.JA)

      if ((audioItem?.audio as Media)?.url) {
        const audio = new Audio((audioItem?.audio as Media)?.url as string)
        audio.play().catch((error) => {
          console.error('Error playing audio:', error)
        })
        return
      }
    }

    setLoadingItemId(item.id)

    // If no audio file, call API
    getKeywordAudioMutation(
      {
        keywordId: item.id,
        language: LocaleEnum.JA,
      },
      {
        onSuccess: (data) => {
          if (data?.url) {
            audioCache.current[item.id] = data.url

            const audio = new Audio(data.url)
            audio.play().catch((error) => {
              console.error('Error playing TTS audio:', error)
            })
          }
        },
        onError: (error) => {
          console.error('Error getting audio from API:', error)
        },
        onSettled: () => {
          setLoadingItemId(null)
        },
      },
    )
  }

  const openKeywordDetailsPopup = (keyword: Keyword, isLoading: boolean) => {
    // Get favorite status from ref or from keyword
    const keywordId = keyword.id || ''
    const isFavorite =
      favoriteKeywordsRef.current[keywordId] !== undefined
        ? favoriteKeywordsRef.current[keywordId]
        : Boolean((keyword as KeywordV2)?.isFavorite)

    openDialog({
      wrapperClassName: '!min-w-[900px] !max-w-[900px] mobile-wrapper mx-auto',
      children: ({ close }) => (
        <KeywordDetailsPopup
          keyword={keyword}
          isLoading={isLoading}
          overrideHandlePlayAudio={handlePlayAudio}
          sheetTitle={t('MES-566')}
          isFavorite={isFavorite}
          onUpdateFavoriteKeywordSuccess={handleUpdateFavoriteKeywordSuccess}
          showFavoriteButton={false}
          isDesktopMode={true}
          closeDialog={close}
          showButtonSaveWord={false}
          showRelatedKeywords={true}
          isCallApi={true}
          showSuggestionFaculties={false}
          showRelatedPosts={true}
          showRelatedProduct={true}
          reOpenRelatedKeyword={(keyword) => {
            close()
            openKeywordDetailsPopup(keyword, false)
          }}
        />
      ),
    })
  }
  if (!keywordList) return null

  return (
    <Section
      className="px-0"
      title={t('MES-751')}
      titleIcon={<Image src={KeywordIcon} alt="icon" height={24} width={24} />}
    >
      <div className="hide-scroll flex">
        <div className="flex w-full flex-col gap-4">
          {keywordList.map((item: Keyword, index) => {
            const localizedName = item?.name as unknown as LocalizeField<string>
            const isLoading = Boolean(item.id && loadingItemId === item.id)

            return (
              <div
                key={item?.id || index}
                className="flex min-w-[200px] flex-1 shrink-0 cursor-pointer snap-start flex-col gap-2 rounded-lg bg-[#F9F9FC] p-4"
                onClick={() => openKeywordDetailsPopup(item, isLoading)}
              >
                <div className="flex items-center justify-between gap-x-2">
                  <div className="flex items-center gap-3">
                    <p className="typo-body-2 text-primary-500">
                      {' '}
                      {localizedName?.[LocaleEnum.JA]}
                    </p>

                    {item?.hiragana && (
                      <p className="typo-body-9 text-subdued">/{item?.hiragana}/</p>
                    )}
                  </div>
                  <div className="size-5 shrink-0 cursor-pointer self-start">
                    {/* <Image
                      src={archiveIcon}
                      alt="audio"
                      width={20}
                      height={20}
                      className="size-5"
                    /> */}
                    {isLoading ? (
                      <Spinner size={5} />
                    ) : (
                      <Image
                        src={audioIcon}
                        alt="audio"
                        width={20}
                        height={20}
                        className="size-5"
                        onClick={(e) => {
                          e.stopPropagation()
                          handlePlayAudio(item)
                        }}
                      />
                    )}
                  </div>
                </div>
                <p className="typo-body-7">{localizedName?.[LocaleEnum.VI]}</p>
              </div>
            )
          })}
        </div>
      </div>
    </Section>
  )
}
