import { useTranslations } from 'next-intl'
import Image from 'next/image'

import SearchIcon from '@/assets/icons/searchIconV2.svg'
import { Keyword, KeywordSearchHistory } from '@/payload-types'

type HomeSearchHistoryProps = {
  keywords: KeywordSearchHistory[]
}

const HomeSearchHistory: React.FC<HomeSearchHistoryProps> = ({ keywords }) => {
  const t = useTranslations()

  return (
    <>
      <div className="typo-heading-8 flex items-center gap-2 text-primary-500">
        {t('MES-776')}
        <Image src={SearchIcon} alt="" width={24} height={24} />
      </div>

      <div className="mt-2 flex flex-wrap gap-2">
        {keywords.map((keyword) => {
          const keywordData = keyword?.keyword as Keyword
          const name = keywordData?.name as string
          return (
            <div
              key={keyword.id}
              className="rounded-[99px] bg-custom-background-hover px-3 py-2 text-subdued"
            >
              {name ?? ''}
            </div>
          )
        })}
      </div>
    </>
  )
}

export default HomeSearchHistory
