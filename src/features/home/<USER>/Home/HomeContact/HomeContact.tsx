'use client'
import { Button } from '@/components/ui/Button/Button'
import { useTranslations } from 'next-intl'

import CallIcon from '@/assets/icons/call.svg'
import Image from 'next/image'

const HomeContact: React.FC = () => {
  const t = useTranslations()
  return (
    <div className="flex flex-col items-center justify-center gap-3 rounded-lg bg-primary-50 p-4">
      <div className="typo-body-3 text-primary-500">{t('MES-633')}</div>

      <a href='https://www.facebook.com/profile.php?id=100066902575956' target='blank'>
        <Button
          variant={'blank'}
          className="typo-button-3 flex items-center gap-x-2 rounded-lg bg-primary-500 px-4 py-3 text-white"
        >
          <Image src={CallIcon} alt="icon phone" width={16} height={16} />
          {t('MES-634')}
        </Button>
      </a>
    </div>
  )
}

export default HomeContact
