'use client'
import { Section } from '@/features/home/<USER>/Section/Section'
import { useTranslations } from 'next-intl'
import Image from 'next/image'
import FeatureBox from './FeatureBox'

// image
import InsuranceIcon from '@/assets/icons/insuranceV2.svg'
import CameraIcon from '@/assets/images/camera.png'
import ResearchByImage from '@/features/product-v2/desktop/common/ResearchByImage'
import { useDialog } from '@/hooks/common/useDialog'
// import DocumentIcon from '@/assets/images/document.svg'
// import Location from '@/assets/images/location.png'
// import FormIcon from '@/assets/images/medical-form.svg'

const FeatureList: React.FC = () => {
  const t = useTranslations()
  const { openDialog } = useDialog()

  const featureList = [
    // {
    //   name: t('MES-753'),
    //   source: Location,
    //   alt: 'Location icon',
    //   backgroundColor: 'bg-[linear-gradient(18deg,#4C9DFF_6.36%,#8DFFE4_85.14%)]',
    //   type: 'location'
    // },
    {
      name: t('MES-754'),
      source: CameraIcon,
      alt: 'camera icon',
      backgroundColor: 'bg-[linear-gradient(246deg,#FFCF82_1.66%,#FF3A6E_92.25%)]',
      type: 'camera',
    },
    // {
    //   name: t('MES-793'),
    //   source: DocumentIcon,
    //   alt: 'document icon',
    //   backgroundColor: 'bg-[linear-gradient(283deg,#EA8DFC_19.22%,#BB6FFE_78.05%)]',
    //   type: 'document'
    // },
    // {
    //   name: t('MES-735'),
    //   source: FormIcon,
    //   alt: 'medical icon',
    //   backgroundColor: 'bg-[linear-gradient(226deg,#E6F060_3.72%,#2FAD2F_96.28%)]',
    //   type: 'form'
    // },
  ]

  const onSelectTab = (type: string) => {
    if (type === 'camera') {
      openDialog({
        children: ({ close }) => <ResearchByImage onClose={close} />,
        variant: 'blank',
        wrapperClassName:
          'mobile-wrapper z-[1000] !h-[353px] !min-w-[826px] overflow-y-auto sm:min-w-0',
      })
    }
  }
  return (
    <Section className="mt-2 px-0">
      <div className="typo-heading-8 flex items-center gap-2 text-primary-500">
        {t('MES-752')}

        <Image src={InsuranceIcon} alt="insurance-icon" height={24} width={24} />
      </div>

      <div className="mt-3 grid grid-cols-2 gap-3">
        {featureList.map((feature) => (
          <FeatureBox
            handleClick={() => onSelectTab(feature.type)}
            key={feature.alt}
            text={feature.name}
            source={feature.source}
            alt={feature.alt}
            bgColor={feature.backgroundColor}
          />
        ))}
      </div>
    </Section>
  )
}

export default FeatureList
