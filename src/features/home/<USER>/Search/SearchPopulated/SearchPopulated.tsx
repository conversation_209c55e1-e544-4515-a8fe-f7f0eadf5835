'use client'
import searchIcon from '@/assets/icons/search-normal.svg'
import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { LocaleEnum } from '@/enums/locale.enum'
import { useGetPopulatedSearchKeywords } from '@/hooks/query/keyword/useGetPopulatedSearchKeywords'
import { LocalizeField } from '@/types/custom-payload.type'
import { useTranslations } from 'next-intl'
import Image from 'next/image'
interface SearchPopulateProps {
  onKeywordClick?: (keyword: string) => void
}
export const SearchPopulated = ({ onKeywordClick }: SearchPopulateProps) => {
  const t = useTranslations()
  const { populatedSearchKeywords, isGetPopulatedSearchKeywordsLoading } =
    useGetPopulatedSearchKeywords({
      params: {
        limit: 6,
      },
      useQueryOptions: {
        staleTime: 5 * 60 * 1000,
      },
    })
  if (isGetPopulatedSearchKeywordsLoading) {
    return (
      <div className="mt-3 rounded-lg bg-white">
        <p className="typo-body-3 mb-4 text-custom-neutral-500">{t('MES-637')}</p>
        <div className="grid grid-cols-3 gap-2">
          {new Array(6).fill(0).map((_, index) => (
            <Skeleton key={index} className="h-[48px] w-full" />
          ))}
        </div>
      </div>
    )
  }
  if (populatedSearchKeywords?.docs.length === 0) {
    return null
  }
  return (
    <div className="mt-3 rounded-lg bg-white">
      <p className="typo-body-3 mb-4 text-custom-neutral-500">{t('MES-637')}</p>
      <div className="grid grid-cols-3 gap-3">
        {populatedSearchKeywords?.docs.map((keyword) => {
          const localizedName = keyword.name as unknown as LocalizeField<string>
          return (
            <div
              key={keyword.id}
              className="flex cursor-pointer items-center justify-between rounded-lg bg-custom-background-hover px-3 py-2"
              onClick={() => onKeywordClick?.(`${localizedName[LocaleEnum.VI]}`)}
            >
              <div>
                <p className="typo-body-7">{localizedName[LocaleEnum.VI]}</p>
                <p className="typo-body-7 text-subdued">{localizedName[LocaleEnum.JA]}</p>
              </div>
              <Image src={searchIcon} alt="search" width={18} height={18} className="size-[18px]" />
            </div>
          )
        })}
      </div>
    </div>
  )
}
