'use client'
import { Spinner } from '@/components/ui/Loading/Spinner'
import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { useAuthentication } from '@/contexts/AuthenticationContext/AuthenticationContext'
import { FacultySearchCard } from '@/features/search-summary/components/SearchResults/FacultiesSearchResult'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import {
  FilterField,
  useGenerateSearchQueryFilter,
} from '@/hooks/common/useGenerateSearchQueryFilter'
import { useGetInfiniteFaculties } from '@/hooks/query/faculty/useGetInfiniteFaculties'
import { Faculty, Subscription } from '@/payload-types'
import { useLocale, useTranslations } from 'next-intl'
import { parse } from 'qs-esm'
import { useEffect } from 'react'
import InfiniteScroll from 'react-infinite-scroll-component'
import SearchPopover from '../SearchPopover/SearchPopover'

type SearchFacultiesProps = {
  paramsValue: {
    [key: string]: string | undefined
  }
  isViewAll?: boolean
  className?: string
  onViewAll?: () => void
  onGetTotalCount: (totalCount: number) => void
  onLoadingData: (isLoading: boolean) => void
}

const renderFaculties = (
  group: Faculty[],
  locale: string,
  secondaryLang: string,
  type: 'free' | 'trial' | 'standard' | 'advanced',
) => (
  <div className="mt-3 grid grid-cols-3 gap-3">
    {group.map((faculty, index) => {
      return (
        <FacultySearchCard
          keywords={[]}
          key={`${faculty.id}-${index}`}
          faculty={faculty}
          locale={locale}
          secondaryLang={secondaryLang}
          userSuscriptionPlan={type}
          bgColor="bg-neutral-100"
          isNative={false}
        ></FacultySearchCard>
      )
    })}
  </div>
)

const SEARCHABLE_FIELDS: FilterField[] = [
  { path: 'symptoms.name', type: 'like', isLocalized: true },
  { path: 'name', type: 'like', isLocalized: true },
  { path: 'bodyParts.name', type: 'like', isLocalized: true },
  // Uncomment if keywords search is needed
  { path: 'questions.question.keywords.name', type: 'like', isLocalized: true },
]

const SearchFaculties: React.FC<SearchFacultiesProps> = ({
  isViewAll = false,
  paramsValue,
  className,
  onViewAll,
  onLoadingData,
  onGetTotalCount,
}) => {
  const t = useTranslations()
  const locale = useLocale()

  const { q: query } = parse(paramsValue as unknown as string)

  const { user } = useAuthentication()
  const { subscription: userSubscription } = user || {}
  const { type } = (userSubscription as Subscription) || {}

  const { queryFilters } = useGenerateSearchQueryFilter({
    query: query,
    searchAbleFields: SEARCHABLE_FIELDS,
  })

  const { secondaryLanguage: secondaryLang } = useAppLanguage()

  const { faculties, isGetFacultiesLoading, fetchNextPage, hasNextPage } = useGetInfiniteFaculties({
    params: {
      limit: isViewAll ? 20 : 6,
      locale: 'all',
      where: {
        and: [
          {
            or: queryFilters,
          },
        ],
      },
    },
    config: {
      enabled: true,
    },
  })

  const flatItems = faculties?.pages.flatMap((page) => page?.docs).filter((item) => !!item) || []
  const totalCount = faculties?.pages[0]?.totalDocs || 0

  const displayItems = isViewAll ? flatItems : flatItems.slice(0, 6)

  useEffect(() => {
    onGetTotalCount(totalCount)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [totalCount])

  useEffect(() => {
    onLoadingData(isGetFacultiesLoading)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isGetFacultiesLoading])

  return (
    <div className={`mt-3 ${className}`}>
      <div className="typo-body-3 flex items-center justify-between">
        <div className="flex items-center gap-3">
          {t('MES-564')} ({totalCount})
          <SearchPopover tooltipContent={t('MES-569')} />
        </div>
        {!isViewAll && onViewAll && totalCount > 3 && (
          <button
            onClick={onViewAll}
            className="typo-body-6 cursor-pointer text-primary-500 hover:text-primary-600"
          >
            {t('MES-476') || 'Xem tất cả'}
          </button>
        )}
      </div>

      {isGetFacultiesLoading && flatItems.length === 0 ? (
        <div className="mt-3 grid grid-cols-3 gap-3">
          {Array.from({ length: 6 }, (_, index) => (
            <Skeleton key={index} className="min-h-[90px] w-full"></Skeleton>
          ))}
        </div>
      ) : isViewAll ? (
        <InfiniteScroll
          dataLength={flatItems.length}
          next={() => {
            if (hasNextPage && !isGetFacultiesLoading) {
              fetchNextPage?.()
            }
          }}
          hasMore={!!hasNextPage}
          loader={
            <div className="flex justify-center py-4">
              <Spinner size={8} />
            </div>
          }
          scrollThreshold={0.8}
        >
          {renderFaculties(displayItems, locale, secondaryLang, type)}
        </InfiniteScroll>
      ) : (
        renderFaculties(displayItems, locale, secondaryLang, type)
      )}
    </div>
  )
}

export default SearchFaculties
