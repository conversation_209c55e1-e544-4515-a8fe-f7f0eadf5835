'use client'

import { Input } from '@/components/ui/Input/Input'
import { useLocale, useTranslations } from 'next-intl'
import Image from 'next/image'
import { useEffect, useState } from 'react'

import CircleClose from '@/assets/icons/close-circle-gray-filled.svg'
import SearchIcon from '@/assets/icons/search-normal.svg'
import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import {
  FilterField,
  useGenerateSearchQueryFilter,
} from '@/hooks/common/useGenerateSearchQueryFilter'
import { useGetKeywords } from '@/hooks/query/keyword/useGetKeywords'
import { parse } from 'qs-esm'

type SearchHeaderProps = {
  paramsValue: {
    [key: string]: string | undefined
  }
  onSearchChange?: (query: string) => void
  searchQuery?: string
}

const SEARCHABLE_FIELDS: FilterField[] = [{ path: 'name', type: 'like', isLocalized: true }]

const SearchHeader: React.FC<SearchHeaderProps> = ({
  paramsValue,
  onSearchChange,
  searchQuery,
}) => {
  const t = useTranslations()

  const locale = useLocale()
  const [inputValue, setInputValue] = useState<string>(searchQuery || '')

  const { q: query } = parse(paramsValue as unknown as string)

  const { queryFilters } = useGenerateSearchQueryFilter({
    query,
    searchAbleFields: SEARCHABLE_FIELDS,
  })

  // Update input value when searchQuery prop changes
  useEffect(() => {
    if (searchQuery !== undefined) setInputValue(searchQuery)
  }, [searchQuery])

  const { keywords, isGetKeywordsLoading } = useGetKeywords({
    params: {
      limit: 5,
      locale: 'all',
      where: {
        and: [
          {
            or: queryFilters,
          },
        ],
      },
    },
  })

  const handleClearSearch = () => {
    setInputValue('')
    onSearchChange?.('')
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setInputValue(value)
    onSearchChange?.(value)
  }

  const handleKeywordClick = (keyword: string) => {
    setInputValue(keyword)
    onSearchChange?.(keyword)
  }

  return (
    <>
      <div className="relative">
        <Input
          placeholder={t('MES-66')}
          className="min-w-[380px] rounded-lg px-4 py-2 pr-6"
          value={inputValue ?? ''}
          onChange={handleInputChange}
        />
        <Image
          alt={inputValue ? 'clear search' : 'search'}
          src={inputValue ? CircleClose : SearchIcon}
          width={20}
          height={20}
          className="absolute right-3 top-1/2 -translate-y-1/2 cursor-pointer"
          onClick={inputValue ? handleClearSearch : undefined}
        />
      </div>

      {/* word related */}
      {keywords && keywords?.docs?.length > 0 && (
        <div className="mt-3 flex flex-wrap items-center gap-2">
          <span className="typo-body-6 text-subdued">{t('MES-638')}:</span>

          {isGetKeywordsLoading
            ? Array.from({ length: 5 }, (_, index) => (
                <Skeleton key={index} className="h-[30px] w-[120px]" />
              ))
            : keywords?.docs.map((keyword) => {
                return (
                  <span
                    key={keyword.id}
                    className="typo-body-7 flex cursor-pointer items-center gap-2 rounded-md border border-primary-500 px-2 py-1 text-primary"
                    onClick={() => handleKeywordClick(keyword.name[locale as string])}
                  >
                    {keyword.name[locale as string]}
                    <Image src={SearchIcon} alt="close" width={16} height={16} />
                  </span>
                )
              })}
        </div>
      )}
    </>
  )
}

export default SearchHeader
