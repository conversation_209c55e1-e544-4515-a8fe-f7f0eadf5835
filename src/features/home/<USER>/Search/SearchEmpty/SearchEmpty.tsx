import { useTranslations } from 'next-intl'
import Image from 'next/image'

import emptyListImage from '@/assets/images/empty-list.svg'

type SearchEmptyProps = {
  className?: string
}
const SearchEmpty: React.FC<SearchEmptyProps> = ({ className }) => {
  const t = useTranslations()
  return (
    <div className={`flex flex-col items-center justify-center gap-6 px-4 py-11 ${className}`}>
      <Image src={emptyListImage} alt="empty" width={180} height={180} />

      <div className="flex flex-col items-center justify-center gap-3">
        <div className="typo-body-3">{t('MES-635')}</div>
        <div className="typo-body-7">{t('MES-636')}.</div>
      </div>
    </div>
  )
}

export default SearchEmpty
