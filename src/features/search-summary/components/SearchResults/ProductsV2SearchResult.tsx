'use client'
import { Spinner } from '@/components/ui/Loading/Spinner'
import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { useWebViewMessaging } from '@/hooks/common/useWebViewMessaging'
import { Media, Product } from '@/payload-types'
import { NATIVE_APP_ROUTES } from '@/routes/nativeAppRoutes'
import { InfiniteData } from '@tanstack/react-query'
import { useTranslations } from 'next-intl'
import Image from 'next/image'
import Link from 'next/link'
import { PaginatedDocs } from 'payload'
import React from 'react'
import InfiniteScroll from 'react-infinite-scroll-component'
import { EmptyList } from '../EmptyList/EmptyList'
import { BaseSearchResult } from './BaseSearchResult'

const LIMIT_RESULTS = 6
interface ProductsV2SearchResultProps {
  products: InfiniteData<PaginatedDocs<Product>, unknown> | undefined
  isGetProductsLoading: boolean
  showAllResults?: boolean
  isInfiniteScroll?: boolean
  fetchNextPage?: () => void
  hasNextPage?: boolean
  onShowMoreClick?: () => void
  showShowMoreButton?: boolean
  isFetchingNextPage?: boolean
  isShowEmptyState?: boolean
  shouldShowTotalDocs?: boolean
  isNative?: boolean
}

export const ProductsV2SearchResult: React.FC<ProductsV2SearchResultProps> = React.memo(
  ({
    products,
    isGetProductsLoading,
    showAllResults = false,
    isInfiniteScroll = false,
    fetchNextPage,
    hasNextPage,
    onShowMoreClick,
    showShowMoreButton = false,
    isFetchingNextPage = false,
    isShowEmptyState = true,
    shouldShowTotalDocs = true,
    isNative = false,
  }) => {
    const t = useTranslations()

    const totalDocs = products?.pages[0]?.totalDocs || 0

    const shouldShowMoreButton = Boolean(
      showShowMoreButton && !isGetProductsLoading && products && products.pages[0]?.docs.length > 0,
    )
    if (!isShowEmptyState && totalDocs === 0 && !isGetProductsLoading) return null
    return (
      <BaseSearchResult
        label={`${t('MES-567')} ${shouldShowTotalDocs ? `(${totalDocs > 99 ? '99+' : totalDocs})` : ''}`}
        onShowMoreClick={onShowMoreClick}
        showShowMoreButton={shouldShowMoreButton}
        tooltipContent={t('MES-570')}
      >
        {/* Loading state */}
        {(isGetProductsLoading || (!products && !isGetProductsLoading)) && (
          <>
            {isInfiniteScroll ? (
              <>
                {!isFetchingNextPage && (
                  <div className="grid grid-cols-2 gap-2">
                    {Array.from({ length: LIMIT_RESULTS }, (_, index) => (
                      <Skeleton
                        key={`skeleton-${index}`}
                        className="aspect-square w-full"
                      ></Skeleton>
                    ))}
                  </div>
                )}
              </>
            ) : (
              <div className="grid grid-cols-2 gap-2">
                {Array.from({ length: LIMIT_RESULTS }, (_, index) => (
                  <Skeleton key={`skeleton-${index}`} className="aspect-square w-full"></Skeleton>
                ))}
              </div>
            )}
          </>
        )}

        {/* Empty state */}
        {products && products.pages[0]?.docs.length === 0 && <EmptyList></EmptyList>}

        {/* Regular view for summary tab */}
        {!isInfiniteScroll && products && products.pages[0]?.docs.length > 0 && (
          <div className="grid grid-cols-2 gap-2">
            {products.pages[0]?.docs
              .slice(0, showAllResults ? products.pages[0]?.docs.length : LIMIT_RESULTS)
              .map((medicine) => (
                <ProductsCard item={medicine} key={`summary-${medicine.id}`} isNative={isNative} />
              ))}
          </div>
        )}

        {/* Infinite scroll for products tab */}
        {isInfiniteScroll && products && products.pages[0]?.docs.length > 0 && (
          <InfiniteScroll
            dataLength={products.pages.flatMap((page) => page.docs).length}
            next={() => {
              if (hasNextPage) {
                fetchNextPage?.()
              }
            }}
            hasMore={!!hasNextPage}
            loader={
              <div className="flex items-center justify-center py-4">
                <Spinner />
              </div>
            }
            scrollThreshold={0.8}
          >
            <div className="grid grid-cols-2 gap-2">
              {products.pages.map((group, pageIndex) =>
                group.docs.map((product) => (
                  <ProductsCard
                    item={product}
                    key={`infinite-${product.id}-${pageIndex}`}
                    isNative={isNative}
                  />
                )),
              )}
            </div>
          </InfiniteScroll>
        )}
      </BaseSearchResult>
    )
  },
)

ProductsV2SearchResult.displayName = 'ProductsV2SearchResult'

interface ProductCard {
  item: Product
  isNative?: boolean
}

export const ProductsCard: React.FC<ProductCard> = (props) => {
  const { item, isNative } = props
  const { title, heroImage, slug } = item

  const banner = (heroImage as Media) || {}
  const { redirectToScreen } = useWebViewMessaging()
  return (
    <Link
      href={`/products-v2/${slug}`}
      className="flex h-[175px] flex-col gap-1 overflow-hidden rounded-[6px] bg-custom-background-hover p-2"
      onClick={(e) => {
        if (isNative) {
          e.preventDefault()
          redirectToScreen(
            NATIVE_APP_ROUTES.PRODUCTS.children?.PRODUCTS_DETAIL_V2.path + `/${slug}`,
          )
        }
      }}
    >
      <div className="relative h-[100px] w-full bg-white">
        {banner.url && (
          <Image
            src={banner.url}
            alt={title || 'medicine'}
            fill
            className="h-full w-full object-contain"
            sizes="300px"
          />
        )}
      </div>

      <p className="typo-body-6 line-clamp-2">{title}</p>
    </Link>
  )
}
